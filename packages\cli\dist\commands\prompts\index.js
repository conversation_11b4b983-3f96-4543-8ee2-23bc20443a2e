/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
export const promptCommand = {
    command: 'prompt <action>',
    describe: 'Manage system prompts',
    builder: (yargs) => {
        return yargs
            .positional('action', {
            describe: 'Action to perform',
            choices: ['list', 'show', 'create', 'edit', 'delete', 'test'],
            demandOption: true,
        })
            .option('id', {
            alias: 'i',
            type: 'string',
            describe: 'Prompt ID',
        })
            .option('category', {
            alias: 'c',
            type: 'string',
            describe: 'Filter by category',
            choices: ['role-based', 'task-specific', 'domain-specific', 'communication-style', 'custom'],
        })
            .option('tag', {
            alias: 't',
            type: 'string',
            describe: 'Filter by tag',
        })
            .option('search', {
            alias: 's',
            type: 'string',
            describe: 'Search prompts',
        })
            .option('file', {
            alias: 'f',
            type: 'string',
            describe: 'File path for import/export',
        })
            .option('output', {
            alias: 'o',
            type: 'string',
            describe: 'Output format',
            choices: ['table', 'json', 'yaml'],
            default: 'table',
        })
            .example('$0 prompt list', 'List all prompts')
            .example('$0 prompt list --category role-based', 'List role-based prompts')
            .example('$0 prompt show --id developer-general', 'Show specific prompt')
            .example('$0 prompt search --search "debugging"', 'Search prompts')
            .example('$0 prompt test --id code-reviewer', 'Test prompt rendering');
    },
    handler: async (argv) => {
        await handlePromptCommand(argv);
    },
};
/**
 * Handle prompt command execution
 */
async function handlePromptCommand(argv) {
    const { action, id, category, tag, search, file, output } = argv;
    try {
        // Get prompt manager from global context (would be injected in real implementation)
        const promptManager = getPromptManager();
        const logger = getLogger();
        switch (action) {
            case 'list':
                const filters = {};
                if (category)
                    filters.category = category;
                if (tag)
                    filters.tag = tag;
                if (search)
                    filters.search = search;
                await listPrompts(promptManager, filters, output || 'table');
                break;
            case 'show':
                if (!id) {
                    console.error('Error: --id is required for show action');
                    process.exit(1);
                }
                await showPrompt(promptManager, id, output || 'table');
                break;
            case 'create':
                await createPrompt(promptManager, file);
                break;
            case 'edit':
                if (!id) {
                    console.error('Error: --id is required for edit action');
                    process.exit(1);
                }
                await editPrompt(promptManager, id);
                break;
            case 'delete':
                if (!id) {
                    console.error('Error: --id is required for delete action');
                    process.exit(1);
                }
                await deletePrompt(promptManager, id);
                break;
            case 'test':
                if (!id) {
                    console.error('Error: --id is required for test action');
                    process.exit(1);
                }
                await testPrompt(promptManager, id);
                break;
            default:
                console.error(`Unknown action: ${action}`);
                process.exit(1);
        }
    }
    catch (error) {
        console.error('Error:', error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
}
/**
 * List prompts
 */
async function listPrompts(promptManager, filters, output) {
    let prompts;
    if (filters.search) {
        prompts = await promptManager.searchPrompts(filters.search);
    }
    else if (filters.category) {
        prompts = await promptManager.listPrompts(filters.category);
    }
    else {
        prompts = await promptManager.listPrompts();
    }
    // Filter by tag if specified
    if (filters.tag) {
        prompts = prompts.filter(p => p.metadata.tags?.includes(filters.tag));
    }
    if (output === 'json') {
        console.log(JSON.stringify(prompts, null, 2));
    }
    else if (output === 'yaml') {
        // Would use yaml library in real implementation
        console.log('YAML output not implemented');
    }
    else {
        // Table format
        console.log('\nAvailable Prompts:');
        console.log('==================');
        if (prompts.length === 0) {
            console.log('No prompts found.');
            return;
        }
        for (const prompt of prompts) {
            console.log(`\n📝 ${prompt.metadata.name} (${prompt.id})`);
            console.log(`   Category: ${prompt.metadata.category || 'custom'}`);
            console.log(`   Description: ${prompt.metadata.description}`);
            if (prompt.metadata.tags?.length) {
                console.log(`   Tags: ${prompt.metadata.tags.join(', ')}`);
            }
        }
    }
}
/**
 * Show specific prompt
 */
async function showPrompt(promptManager, id, output) {
    const prompt = await promptManager.getPrompt({ id });
    if (!prompt) {
        console.error(`Prompt not found: ${id}`);
        process.exit(1);
    }
    if (output === 'json') {
        console.log(JSON.stringify(prompt, null, 2));
    }
    else {
        console.log(`\n📝 ${prompt.metadata.name}`);
        console.log('='.repeat(prompt.metadata.name.length + 3));
        console.log(`ID: ${prompt.id}`);
        console.log(`Category: ${prompt.metadata.category || 'custom'}`);
        console.log(`Description: ${prompt.metadata.description}`);
        console.log(`Version: ${prompt.metadata.version}`);
        if (prompt.metadata.author) {
            console.log(`Author: ${prompt.metadata.author}`);
        }
        if (prompt.metadata.tags?.length) {
            console.log(`Tags: ${prompt.metadata.tags.join(', ')}`);
        }
        if (prompt.variables?.length) {
            console.log(`Variables: ${prompt.variables.join(', ')}`);
        }
        console.log('\nContent:');
        console.log('--------');
        console.log(prompt.content);
    }
}
/**
 * Create new prompt (placeholder)
 */
async function createPrompt(_promptManager, file) {
    console.log('Create prompt functionality would be implemented here');
    console.log('This would open an editor or import from file:', file);
}
/**
 * Edit prompt (placeholder)
 */
async function editPrompt(_promptManager, id) {
    console.log(`Edit prompt functionality would be implemented here for: ${id}`);
    console.log('This would open an editor with the current prompt content');
}
/**
 * Delete prompt (placeholder)
 */
async function deletePrompt(_promptManager, id) {
    console.log(`Delete prompt functionality would be implemented here for: ${id}`);
    console.log('This would confirm and delete the prompt');
}
/**
 * Test prompt rendering
 */
async function testPrompt(promptManager, id) {
    const context = promptManager.getContext();
    const rendered = await promptManager.renderPrompt({ id }, context);
    if (!rendered) {
        console.error(`Failed to render prompt: ${id}`);
        process.exit(1);
    }
    console.log(`\n🧪 Testing prompt: ${rendered.metadata.name}`);
    console.log('='.repeat(rendered.metadata.name.length + 18));
    console.log('\nRendered Content:');
    console.log('-'.repeat(16));
    console.log(rendered.content);
    if (Object.keys(rendered.variables).length > 0) {
        console.log('\nVariables Used:');
        console.log('-'.repeat(14));
        for (const [key, value] of Object.entries(rendered.variables)) {
            console.log(`  ${key}: ${value}`);
        }
    }
}
/**
 * Get prompt manager from global context (placeholder)
 */
function getPromptManager() {
    // In real implementation, this would be injected from the main application context
    throw new Error('Prompt manager not available - this is a placeholder implementation');
}
/**
 * Get logger from global context (placeholder)
 */
function getLogger() {
    // In real implementation, this would be injected from the main application context
    return console;
}
//# sourceMappingURL=index.js.map