/**
 * Synchronous validation of a path existing either as a file or as a directory.
 *
 * @param {string} path The path to check
 * @param {number} type One or both of the exported numeric constants
 */
export declare function exists(path: string, type?: number): boolean;
/**
 * Constant representing a file
 */
export declare const FILE = 1;
/**
 * Constant representing a folder
 */
export declare const FOLDER = 2;
/**
 * Constant representing either a file or a folder
 */
export declare const READABLE: number;
