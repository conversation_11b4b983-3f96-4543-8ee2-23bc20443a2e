{"version": 3, "file": "registry.js", "sourceRoot": "", "sources": ["../../../src/prompts/loaders/registry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAUH,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAE7C;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IACvB,OAAO,GAAG,IAAI,GAAG,EAA8B,CAAC;IAChD,MAAM,CAAqB;IAEnC,YAAY,MAAe;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAA6B;QAC5C,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,qCAAqC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAEtE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,6BAA6B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;gBACjE,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACxC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,uBAAuB,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3F,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,gCAAgC,MAAM,CAAC,MAAM,EAAE,EAAE;oBAClE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,UAAU,GAAqB,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QAEzD,2CAA2C;QAC3C,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE5D,KAAK,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,aAAa,EAAE,CAAC;YAC7C,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,yBAAyB,MAAM,EAAE,CAAC,CAAC;gBACtD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpC,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;gBAC5B,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,uBAAuB,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAChF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,+BAA+B,MAAM,EAAE,EAAE;oBAC1D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,kDAAkD;QAClD,MAAM,aAAa,GAAG,IAAI,GAAG,EAA0B,CAAC;QACxD,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAoB;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACH,IAAI,MAAM,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC5B,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,oCAAoC,EAAE;oBACtD,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,EAAE;oBACF,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,OAAyB,EAAE,SAAuB,SAAS;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,oBAAoB,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,6BAA6B,MAAM,EAAE,EAAE;gBACxD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,MAA0B;QAC7C,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,QAAQ;gBACX,OAAO,IAAI,gBAAgB,CACzB,QAAQ,EACR,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAC1C,IAAI,CAAC,MAAM,CACZ,CAAC;YAEJ,KAAK,SAAS;gBACZ,OAAO,IAAI,gBAAgB,CACzB,SAAS,EACT,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAC3C,IAAI,CAAC,MAAM,CACZ,CAAC;YAEJ,KAAK,SAAS;gBACZ,4DAA4D;gBAC5D,OAAO,IAAI,gBAAgB,CACzB,SAAS,EACT,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAC3C,IAAI,CAAC,MAAM,CACZ,CAAC;YAEJ,KAAK,UAAU;gBACb,2DAA2D;gBAC3D,wCAAwC;gBACxC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAE5E;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE,CAAC;QAElE,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;YACnB,OAAO,GAAG,OAAO,kBAAkB,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,OAAO,gBAAgB,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC;QAEhE,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;YACnB,OAAO,GAAG,MAAM,wBAAwB,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,MAAM,uBAAuB,CAAC;QAC1C,CAAC;IACH,CAAC;CACF"}