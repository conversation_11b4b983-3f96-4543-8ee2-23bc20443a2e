/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  Provider,
  ProviderConfig,
  ProviderType,
  ProviderId,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  Logger,
  PromptManager,
  PromptContext,
} from '@inkbytefo/s647-shared';
import { ProviderFactory } from './providers/factory.js';
import { ProviderRegistry } from './providers/registry.js';

/**
 * AI Manager - Central coordinator for AI providers
 */
export class AIManager {
  private factory: ProviderFactory;
  private registry: ProviderRegistry;
  private defaultProviderId?: ProviderId;
  private logger: Logger | undefined;
  private promptManager?: PromptManager;

  constructor(logger?: Logger) {
    this.factory = ProviderFactory.getInstance();
    this.registry = ProviderRegistry.getInstance();
    this.logger = logger;
  }

  /**
   * Set the prompt manager for system prompt injection
   */
  public setPromptManager(promptManager: PromptManager): void {
    this.promptManager = promptManager;
    this.logger?.info('Prompt manager set for AI Manager');
  }

  /**
   * Initialize the AI manager with provider configurations
   */
  public async initialize(
    configs: Record<string, ProviderConfig>,
    defaultProvider?: string
  ): AsyncResult<void> {
    try {
      this.logger?.info('Initializing AI Manager with providers', { configs: Object.keys(configs) });

      // Create and register providers
      for (const [id, config] of Object.entries(configs)) {
        if (config.enabled === false) {
          this.logger?.debug(`Skipping disabled provider: ${id}`);
          continue;
        }

        const providerId = id as ProviderId;
        const result = await this.factory.create(providerId, config);
        
        if (result.success) {
          this.registry.register(result.data);
          this.logger?.info(`Successfully initialized provider: ${id}`, { type: config.type });
        } else {
          this.logger?.error(`Failed to initialize provider: ${id}`, { error: result.error.message });
        }
      }

      // Set default provider
      if (defaultProvider) {
        this.defaultProviderId = defaultProvider as ProviderId;
      } else {
        // Use the first available provider as default
        const available = this.registry.getAvailable();
        if (available.length > 0) {
          this.defaultProviderId = available[0]!.id;
        }
      }

      if (!this.defaultProviderId) {
        throw new Error('No providers available');
      }

      this.logger?.info('AI Manager initialized successfully', {
        defaultProvider: this.defaultProviderId,
        availableProviders: this.registry.getAvailable().length,
      });

      return { success: true, data: undefined };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger?.error('Failed to initialize AI Manager', { error: err.message });
      return { success: false, error: err };
    }
  }

  /**
   * Get the default provider
   */
  public getDefaultProvider(): Provider | undefined {
    if (!this.defaultProviderId) {
      return undefined;
    }
    return this.registry.get(this.defaultProviderId);
  }

  /**
   * Get a provider by ID
   */
  public getProvider(id: ProviderId): Provider | undefined {
    return this.registry.get(id);
  }

  /**
   * Get all available providers
   */
  public getAvailableProviders(): Provider[] {
    return this.registry.getAvailable();
  }

  /**
   * Get providers by type
   */
  public getProvidersByType(type: ProviderType): Provider[] {
    return this.registry.getByType(type);
  }

  /**
   * Find the best provider for a model
   */
  public findBestProvider(modelId: string): Provider | undefined {
    return this.registry.findBestProvider(modelId);
  }

  /**
   * Create a chat completion using the best available provider
   */
  public async createChatCompletion(
    request: ChatCompletionRequest,
    providerId?: ProviderId,
    context?: PromptContext
  ): AsyncResult<ChatCompletionResponse> {
    try {
      const provider = this.selectProvider(request.model, providerId);
      if (!provider) {
        throw new Error('No suitable provider found');
      }

      // Inject system prompt if available
      const enhancedRequest = await this.injectSystemPrompt(request, context);

      this.logger?.debug('Creating chat completion', {
        provider: provider.id,
        model: enhancedRequest.model,
        messageCount: enhancedRequest.messages.length,
        hasSystemPrompt: enhancedRequest.messages.some((m: ChatMessage) => m.role === 'system'),
      });

      return await provider.createChatCompletion(enhancedRequest);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger?.error('Failed to create chat completion', { error: err.message });
      return { success: false, error: err };
    }
  }

  /**
   * Create a streaming chat completion using the best available provider
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest,
    providerId?: ProviderId,
    context?: PromptContext
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      const provider = this.selectProvider(request.model, providerId);
      if (!provider) {
        throw new Error('No suitable provider found');
      }

      // Inject system prompt if available
      const enhancedRequest = await this.injectSystemPrompt(request, context);

      this.logger?.debug('Creating streaming chat completion', {
        provider: provider.id,
        model: enhancedRequest.model,
        messageCount: enhancedRequest.messages.length,
        hasSystemPrompt: enhancedRequest.messages.some((m: ChatMessage) => m.role === 'system'),
      });

      return await provider.createChatCompletionStream(enhancedRequest);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger?.error('Failed to create streaming chat completion', { error: err.message });
      return { success: false, error: err };
    }
  }

  /**
   * Create embeddings using the best available provider
   */
  public async createEmbedding(
    request: EmbeddingRequest,
    providerId?: ProviderId
  ): AsyncResult<EmbeddingResponse> {
    try {
      const provider = this.selectProvider(request.model, providerId);
      if (!provider) {
        throw new Error('No suitable provider found');
      }

      this.logger?.debug('Creating embedding', {
        provider: provider.id,
        model: request.model,
        inputType: typeof request.input,
      });

      return await provider.createEmbedding(request);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger?.error('Failed to create embedding', { error: err.message });
      return { success: false, error: err };
    }
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[],
    modelId?: string,
    providerId?: ProviderId
  ): AsyncResult<number> {
    try {
      const provider = this.selectProvider(modelId, providerId);
      if (!provider) {
        throw new Error('No suitable provider found');
      }

      return await provider.countTokens(input);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger?.error('Failed to count tokens', { error: err.message });
      return { success: false, error: err };
    }
  }

  /**
   * Get available models from all providers
   */
  public async getAvailableModels(): AsyncResult<ModelInfo[]> {
    try {
      const providers = this.registry.getAvailable();
      const allModels: ModelInfo[] = [];

      for (const provider of providers) {
        const result = await provider.getModels();
        if (result.success) {
          allModels.push(...result.data);
        } else {
          this.logger?.warn(`Failed to get models from provider ${provider.id}`, {
            error: result.error.message,
          });
        }
      }

      return { success: true, data: allModels };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger?.error('Failed to get available models', { error: err.message });
      return { success: false, error: err };
    }
  }

  /**
   * Get provider statistics
   */
  public getStats() {
    return this.registry.getStats();
  }

  /**
   * Dispose of all providers
   */
  public async dispose(): Promise<void> {
    this.logger?.info('Disposing AI Manager');
    await this.registry.dispose();
  }

  /**
   * Select the best provider for a request
   */
  private selectProvider(modelId?: string, providerId?: ProviderId): Provider | undefined {
    // If a specific provider is requested, use it
    if (providerId) {
      const provider = this.registry.get(providerId);
      if (provider && provider.status === 'available') {
        return provider;
      }
    }

    // If a model is specified, find the best provider for it
    if (modelId) {
      const provider = this.registry.findBestProvider(modelId);
      if (provider) {
        return provider;
      }
    }

    // Fall back to the default provider
    return this.getDefaultProvider();
  }

  /**
   * Inject system prompt into chat completion request
   */
  private async injectSystemPrompt(
    request: ChatCompletionRequest,
    context?: PromptContext
  ): Promise<ChatCompletionRequest> {
    if (!this.promptManager) {
      return request;
    }

    try {
      // Check if there's already a system message
      const hasSystemMessage = request.messages.some((m: ChatMessage) => m.role === 'system');

      if (hasSystemMessage) {
        this.logger?.debug('System message already present, skipping prompt injection');
        return request;
      }

      // Get default prompt or use context to select appropriate prompt
      const promptSelector = context?.currentTask
        ? { id: context.currentTask }
        : { id: 'developer-general' };

      const renderedPrompt = await this.promptManager.renderPrompt(promptSelector, context || {});

      if (!renderedPrompt) {
        this.logger?.debug('No system prompt found, proceeding without injection');
        return request;
      }

      // Create system message
      const systemMessage: ChatMessage = {
        role: 'system',
        content: renderedPrompt.content,
        timestamp: Date.now(),
      };

      // Inject system message at the beginning
      const enhancedRequest: ChatCompletionRequest = {
        ...request,
        messages: [systemMessage, ...request.messages],
      };

      this.logger?.debug('System prompt injected', {
        promptId: renderedPrompt.id,
        promptLength: renderedPrompt.content.length,
      });

      return enhancedRequest;
    } catch (error) {
      this.logger?.warn('Failed to inject system prompt', {
        error: error instanceof Error ? error.message : String(error),
      });
      return request;
    }
  }
}
