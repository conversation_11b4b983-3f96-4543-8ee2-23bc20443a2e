import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Cancel Fine Tuning Job
 *
 * @remarks
 * Request the cancellation of a fine tuning job.
 */
export declare function fineTuningJobsCancel(client: MistralCore, request: operations.JobsApiRoutesFineTuningCancelFineTuningJobRequest, options?: RequestOptions): APIPromise<Result<operations.JobsApiRoutesFineTuningCancelFineTuningJobResponse, SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=fineTuningJobsCancel.d.ts.map