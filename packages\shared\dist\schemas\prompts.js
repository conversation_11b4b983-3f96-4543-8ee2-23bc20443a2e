/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
/**
 * Prompt schemas using Zod for runtime validation
 */
export const PromptVariableSchema = z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.unknown()
]);
export const PromptContextSchema = z.object({
    // Project context
    projectName: z.string().optional(),
    projectType: z.string().optional(),
    language: z.string().optional(),
    framework: z.string().optional(),
    // File context
    fileName: z.string().optional(),
    fileExtension: z.string().optional(),
    filePath: z.string().optional(),
    // User context
    userName: z.string().optional(),
    userRole: z.string().optional(),
    // Session context
    currentTask: z.string().optional(),
    sessionId: z.string().optional(),
    timestamp: z.number().optional(),
}).catchall(PromptVariableSchema);
export const PromptCategorySchema = z.enum([
    'role-based',
    'task-specific',
    'domain-specific',
    'communication-style',
    'custom'
]);
export const PromptMetadataSchema = z.object({
    name: z.string(),
    description: z.string(),
    version: z.string(),
    author: z.string().optional(),
    tags: z.array(z.string()).optional(),
    category: PromptCategorySchema.optional(),
    language: z.string().optional(),
    createdAt: z.number().optional(),
    updatedAt: z.number().optional(),
});
export const PromptConditionSchema = z.object({
    variable: z.string(),
    operator: z.enum(['equals', 'not-equals', 'contains', 'not-contains', 'exists', 'not-exists']),
    value: PromptVariableSchema.optional(),
});
export const PromptTemplateSchema = z.object({
    id: z.string(),
    metadata: PromptMetadataSchema,
    content: z.string(),
    variables: z.array(z.string()).optional(),
    conditions: z.array(PromptConditionSchema).optional(),
    extends: z.string().optional(),
    priority: z.number().optional(),
});
export const RenderedPromptSchema = z.object({
    id: z.string(),
    content: z.string(),
    metadata: PromptMetadataSchema,
    context: PromptContextSchema,
    variables: z.record(PromptVariableSchema),
});
export const PromptSourceSchema = z.enum(['built-in', 'global', 'project', 'session']);
export const PromptLoaderConfigSchema = z.object({
    source: PromptSourceSchema,
    path: z.string().optional(),
    enabled: z.boolean(),
    priority: z.number(),
});
export const PromptManagerConfigSchema = z.object({
    defaultPrompt: z.string().optional(),
    loaders: z.array(PromptLoaderConfigSchema),
    templateEngine: z.enum(['mustache', 'handlebars']).optional(),
    cacheEnabled: z.boolean().optional(),
    cacheTtl: z.number().optional(),
});
export const PromptSelectorSchema = z.object({
    id: z.string().optional(),
    category: PromptCategorySchema.optional(),
    tags: z.array(z.string()).optional(),
    context: PromptContextSchema.partial().optional(),
});
export const PromptValidationResultSchema = z.object({
    valid: z.boolean(),
    errors: z.array(z.string()),
    warnings: z.array(z.string()),
});
export const BuiltInPromptIdSchema = z.enum([
    // Role-based prompts
    'developer-general',
    'code-reviewer',
    'debugger',
    'architect',
    'mentor',
    'analyst',
    // Task-specific prompts
    'documentation',
    'testing',
    'refactoring',
    'optimization',
    'security',
    'deployment',
    // Communication style prompts
    'concise',
    'detailed',
    'step-by-step',
    'creative',
    'formal',
    'casual',
    // Domain-specific prompts
    'web-development',
    'mobile-development',
    'data-science',
    'devops',
    'machine-learning'
]);
//# sourceMappingURL=prompts.js.map