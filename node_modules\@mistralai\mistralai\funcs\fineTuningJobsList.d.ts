import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Get Fine Tuning Jobs
 *
 * @remarks
 * Get a list of fine-tuning jobs for your organization and user.
 */
export declare function fineTuningJobsList(client: MistralCore, request?: operations.JobsApiRoutesFineTuningGetFineTuningJobsRequest | undefined, options?: RequestOptions): APIPromise<Result<components.JobsOut, SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=fineTuningJobsList.d.ts.map