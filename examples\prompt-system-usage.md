# S647 Sistem Promptu Sistemi - Kullanım Kılavuzu

## 🎯 Genel Bakış

S647 Refactored artık gü<PERSON>ü bir sistem promptu sistemi ile gelir. Bu sistem, AI asistanınızın davranışını ve kişiliğini özelleştirmenize olanak tanır.

## 🚀 Temel Özellikler

### 1. **Hazır Prompt Kütüphanesi**
- **Role-based**: developer-general, code-reviewer, debugger, architect, mentor
- **Task-specific**: documentation, testing, refactoring, optimization, security
- **Communication-style**: concise, detailed, step-by-step, creative, formal

### 2. **Template Sistemi**
- Mustache-style değişken interpolasyonu: `{{variable}}`
- Koşullu bloklar: `{{#if condition}}...{{/if}}`
- Built-in değişkenler: `{{date}}`, `{{time}}`, `{{projectName}}`

### 3. **Hiyerarşik Yükleme**
```
Built-in prompts (en düşük öncelik)
├── Global user prompts (~/.s647/prompts/)
│   └── Project prompts (.s647/prompts/)
│       └── Session prompts (runtime) (en yüksek öncelik)
```

## 📋 Kullanım Örnekleri

### Temel Kullanım

```typescript
import { PromptManagerImpl, BuiltInPromptLibrary } from '@inkbytefo/s647-core';

// Prompt manager oluştur
const promptManager = new PromptManagerImpl();

// Konfigürasyon
const config = {
  defaultPrompt: 'developer-general',
  loaders: [
    { source: 'built-in', enabled: true, priority: 10 },
    { source: 'project', enabled: true, priority: 75 }
  ],
  templateEngine: 'mustache',
  cacheEnabled: true
};

await promptManager.initialize(config);
```

### AI Manager ile Entegrasyon

```typescript
import { AIManager } from '@inkbytefo/s647-core';

const aiManager = new AIManager();
const promptManager = new PromptManagerImpl();

// Prompt manager'ı AI manager'a bağla
aiManager.setPromptManager(promptManager);

// Context ile chat completion
const context = {
  projectName: 'MyProject',
  language: 'TypeScript',
  currentTask: 'debugging'
};

const response = await aiManager.createChatCompletion({
  model: 'gpt-4',
  messages: [
    { role: 'user', content: 'Bu kodda bir hata var, yardım eder misin?' }
  ]
}, undefined, context);
```

### Özel Prompt Oluşturma

```typescript
const customPrompt = {
  id: 'my-custom-prompt',
  metadata: {
    name: 'Özel Geliştirici Asistanı',
    description: 'Türkçe konuşan özel asistan',
    version: '1.0.0',
    category: 'custom',
    tags: ['turkish', 'custom']
  },
  content: `Sen Türkçe konuşan bir yazılım geliştirici asistanısın.

{{#if projectName}}
Şu anda {{projectName}} projesi üzerinde çalışıyorsun.
{{/if}}

{{#if language}}
Kullanılan dil: {{language}}
{{/if}}

Her zaman:
- Türkçe yanıt ver
- Kod örnekleri ile açıkla
- Best practice'leri öner
- Güvenlik ve performansı göz önünde bulundur`,
  variables: ['projectName', 'language']
};

await promptManager.addPrompt(customPrompt);
```

### CLI Komutları

```bash
# Mevcut promptları listele
s647 prompt list

# Kategori bazında filtrele
s647 prompt list --category role-based

# Belirli bir promptu göster
s647 prompt show --id developer-general

# Prompt ara
s647 prompt search --search "debugging"

# Prompt test et
s647 prompt test --id code-reviewer
```

## 🎨 Template Örnekleri

### Basit Değişken Kullanımı

```mustache
Merhaba! Ben {{userName || 'Geliştirici'}} için bir AI asistanıyım.

{{#if projectName}}
Şu anda {{projectName}} projesi üzerinde çalışıyoruz.
{{/if}}

Bugün {{date}} tarihinde, saat {{time}}'da konuşuyoruz.
```

### Koşullu Bloklar

```mustache
{{#if language === 'typescript'}}
TypeScript kullanıyorsun, harika! Type safety'ye özel dikkat edeceğim.
{{/if}}

{{#if framework === 'react'}}
React projesi için component best practice'lerini uygulayacağım.
{{/if}}

{{#if currentTask === 'debugging'}}
Hata ayıklama modundayım. Sistematik yaklaşım kullanacağım:
1. Problemi anlama
2. Root cause analizi
3. Çözüm önerisi
4. Test stratejisi
{{/if}}
```

### Proje Bazlı Konfigürasyon

`.s647/prompts/custom.json`:
```json
[
  {
    "id": "project-specific",
    "metadata": {
      "name": "Proje Özel Asistan",
      "description": "Bu proje için özelleştirilmiş asistan",
      "version": "1.0.0",
      "category": "custom"
    },
    "content": "Bu {{projectName}} projesi için özel yapılandırılmış asistanım...",
    "variables": ["projectName"],
    "priority": 100
  }
]
```

## 🔧 Gelişmiş Konfigürasyon

### Environment Variables

```bash
# Varsayılan prompt
S647_PROMPTS_DEFAULT_PROMPT=developer-general

# Cache ayarları
S647_PROMPTS_CACHE_ENABLED=true
S647_PROMPTS_CACHE_TTL=300000

# Template engine
S647_PROMPTS_TEMPLATE_ENGINE=mustache
```

### Programmatik Konfigürasyon

```typescript
const advancedConfig = {
  defaultPrompt: 'architect',
  loaders: [
    {
      source: 'built-in',
      enabled: true,
      priority: 10
    },
    {
      source: 'global',
      enabled: true,
      priority: 50,
      path: '~/.s647/prompts'
    },
    {
      source: 'project',
      enabled: true,
      priority: 75,
      path: '.s647/prompts'
    },
    {
      source: 'session',
      enabled: true,
      priority: 100
    }
  ],
  templateEngine: 'mustache',
  cacheEnabled: true,
  cacheTtl: 300000 // 5 dakika
};
```

## 🎯 Best Practices

### 1. **Prompt Organizasyonu**
- Kategori bazında organize edin
- Anlamlı ID'ler kullanın
- Versiyonlama yapın
- Tag'ler ile gruplandırın

### 2. **Template Tasarımı**
- Değişkenleri açık şekilde tanımlayın
- Koşullu blokları akıllıca kullanın
- Fallback değerler sağlayın
- Test edilebilir yapıda yazın

### 3. **Context Yönetimi**
- Gerekli context'i önceden hazırlayın
- Performans için cache kullanın
- Session bazında context güncelleyin

### 4. **Güvenlik**
- Hassas bilgileri template'lerde saklamayın
- Input validation yapın
- Sandbox kullanımını düşünün

## 🧪 Test ve Debug

### Prompt Test Etme

```typescript
// Prompt'u test et
const testContext = {
  projectName: 'TestProject',
  language: 'TypeScript',
  userName: 'Developer'
};

const rendered = await promptManager.renderPrompt(
  { id: 'developer-general' },
  testContext
);

console.log('Rendered prompt:', rendered.content);
console.log('Used variables:', rendered.variables);
```

### Validation

```typescript
const validation = promptManager.validatePrompt(customPrompt);
if (!validation.valid) {
  console.error('Validation errors:', validation.errors);
  console.warn('Warnings:', validation.warnings);
}
```

## 🚀 Gelecek Özellikler

- **Prompt Marketplace**: Topluluk promptları
- **A/B Testing**: Prompt performans karşılaştırması
- **Analytics**: Prompt kullanım istatistikleri
- **Visual Editor**: GUI tabanlı prompt editörü
- **Multi-language**: Çoklu dil desteği

---

Bu sistem promptu sistemi ile S647'nizi tamamen kişiselleştirebilir ve projelerinize özel AI asistanları oluşturabilirsiniz! 🎉
