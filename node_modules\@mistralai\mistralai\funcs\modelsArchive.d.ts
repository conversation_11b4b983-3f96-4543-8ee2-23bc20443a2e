import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Archive Fine Tuned Model
 *
 * @remarks
 * Archive a fine-tuned model.
 */
export declare function modelsArchive(client: MistralCore, request: operations.JobsApiRoutesFineTuningArchiveFineTunedModelRequest, options?: RequestOptions): APIPromise<Result<components.ArchiveFTModelOut, SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=modelsArchive.d.ts.map