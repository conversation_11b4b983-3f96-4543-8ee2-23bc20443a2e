{"version": 3, "file": "manager.js", "sourceRoot": "", "sources": ["../../src/ai/manager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAmBH,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,SAAS;IACZ,OAAO,CAAkB;IACzB,QAAQ,CAAmB;IAC3B,iBAAiB,CAAc;IAC/B,MAAM,CAAqB;IAC3B,aAAa,CAAiB;IAEtC,YAAY,MAAe;QACzB,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,aAA4B;QAClD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU,CACrB,OAAuC,EACvC,eAAwB;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,wCAAwC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE/F,gCAAgC;YAChC,KAAK,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnD,IAAI,MAAM,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBAC7B,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC;oBACxD,SAAS;gBACX,CAAC;gBAED,MAAM,UAAU,GAAG,EAAgB,CAAC;gBACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAE7D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACpC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,sCAAsC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvF,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,kCAAkC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9F,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,IAAI,eAAe,EAAE,CAAC;gBACpB,IAAI,CAAC,iBAAiB,GAAG,eAA6B,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,8CAA8C;gBAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC/C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,CAAC,CAAE,CAAC,EAAE,CAAC;gBAC5C,CAAC;YACH,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,qCAAqC,EAAE;gBACvD,eAAe,EAAE,IAAI,CAAC,iBAAiB;gBACvC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,MAAM;aACxD,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,EAAc;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,qBAAqB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,IAAkB;QAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,OAAe;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAC/B,OAA8B,EAC9B,UAAuB,EACvB,OAAuB;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAChE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,oCAAoC;YACpC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAExE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,0BAA0B,EAAE;gBAC7C,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBACrB,KAAK,EAAE,eAAe,CAAC,KAAK;gBAC5B,YAAY,EAAE,eAAe,CAAC,QAAQ,CAAC,MAAM;gBAC7C,eAAe,EAAE,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAc,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;aACxF,CAAC,CAAC;YAEH,OAAO,MAAM,QAAQ,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,0BAA0B,CACrC,OAA8B,EAC9B,UAAuB,EACvB,OAAuB;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAChE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,oCAAoC;YACpC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAExE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,oCAAoC,EAAE;gBACvD,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBACrB,KAAK,EAAE,eAAe,CAAC,KAAK;gBAC5B,YAAY,EAAE,eAAe,CAAC,QAAQ,CAAC,MAAM;gBAC7C,eAAe,EAAE,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAc,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;aACxF,CAAC,CAAC;YAEH,OAAO,MAAM,QAAQ,CAAC,0BAA0B,CAAC,eAAe,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,4CAA4C,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACzF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAC1B,OAAyB,EACzB,UAAuB;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAChE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,oBAAoB,EAAE;gBACvC,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBACrB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,SAAS,EAAE,OAAO,OAAO,CAAC,KAAK;aAChC,CAAC,CAAC;YAEH,OAAO,MAAM,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACtB,KAA6B,EAC7B,OAAgB,EAChB,UAAuB;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC/C,MAAM,SAAS,GAAgB,EAAE,CAAC;YAElC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC1C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,sCAAsC,QAAQ,CAAC,EAAE,EAAE,EAAE;wBACrE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;qBAC5B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAgB,EAAE,UAAuB;QAC9D,8CAA8C;QAC9C,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAChD,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,oCAAoC;QACpC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,OAA8B,EAC9B,OAAuB;QAEvB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAc,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAExF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,2DAA2D,CAAC,CAAC;gBAChF,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,iEAAiE;YACjE,MAAM,cAAc,GAAG,OAAO,EAAE,WAAW;gBACzC,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,WAAW,EAAE;gBAC7B,CAAC,CAAC,EAAE,EAAE,EAAE,mBAAmB,EAAE,CAAC;YAEhC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;YAE5F,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,sDAAsD,CAAC,CAAC;gBAC3E,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,wBAAwB;YACxB,MAAM,aAAa,GAAgB;gBACjC,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,cAAc,CAAC,OAAO;gBAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,yCAAyC;YACzC,MAAM,eAAe,GAA0B;gBAC7C,GAAG,OAAO;gBACV,QAAQ,EAAE,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;aAC/C,CAAC;YAEF,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,wBAAwB,EAAE;gBAC3C,QAAQ,EAAE,cAAc,CAAC,EAAE;gBAC3B,YAAY,EAAE,cAAc,CAAC,OAAO,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,gCAAgC,EAAE;gBAClD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;CACF"}