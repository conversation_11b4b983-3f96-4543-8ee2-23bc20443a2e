/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { promises as fs } from 'fs';
import { join, extname } from 'path';
import { PromptTemplateSchema } from '@inkbytefo/s647-shared';
/**
 * File-based prompt loader
 * Loads prompts from JSON files in a directory
 */
export class FilePromptLoader {
    source;
    priority;
    path;
    logger;
    constructor(source, priority, path, logger) {
        this.source = source;
        this.priority = priority;
        this.path = path;
        this.logger = logger;
    }
    /**
     * Load prompts from the configured directory
     */
    async load() {
        try {
            // Check if directory exists
            const exists = await this.directoryExists();
            if (!exists) {
                this.logger?.debug(`Prompt directory does not exist: ${this.path}`);
                return [];
            }
            const files = await fs.readdir(this.path);
            const promptFiles = files.filter(file => extname(file).toLowerCase() === '.json');
            const prompts = [];
            for (const file of promptFiles) {
                try {
                    const filePath = join(this.path, file);
                    const content = await fs.readFile(filePath, 'utf-8');
                    const data = JSON.parse(content);
                    // Handle both single prompt and array of prompts
                    const promptsData = Array.isArray(data) ? data : [data];
                    for (const promptData of promptsData) {
                        try {
                            const prompt = PromptTemplateSchema.parse(promptData);
                            prompts.push(prompt);
                        }
                        catch (error) {
                            this.logger?.warn(`Invalid prompt in file ${file}`, {
                                error: error instanceof Error ? error.message : String(error)
                            });
                        }
                    }
                }
                catch (error) {
                    this.logger?.warn(`Failed to load prompt file: ${file}`, {
                        error: error instanceof Error ? error.message : String(error)
                    });
                }
            }
            return prompts;
        }
        catch (error) {
            this.logger?.error(`Failed to load prompts from ${this.path}`, {
                error: error instanceof Error ? error.message : String(error)
            });
            return [];
        }
    }
    /**
     * Save prompts to the configured directory
     */
    async save(templates) {
        try {
            // Ensure directory exists
            await this.ensureDirectory();
            // Group prompts by category for better organization
            const promptsByCategory = new Map();
            for (const template of templates) {
                const category = template.metadata.category || 'custom';
                if (!promptsByCategory.has(category)) {
                    promptsByCategory.set(category, []);
                }
                promptsByCategory.get(category).push(template);
            }
            // Save each category to a separate file
            for (const [category, prompts] of promptsByCategory) {
                const fileName = `${category}.json`;
                const filePath = join(this.path, fileName);
                const content = JSON.stringify(prompts, null, 2);
                await fs.writeFile(filePath, content, 'utf-8');
                this.logger?.debug(`Saved ${prompts.length} prompts to ${fileName}`);
            }
        }
        catch (error) {
            this.logger?.error(`Failed to save prompts to ${this.path}`, {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Check if a prompt exists
     */
    async exists(id) {
        try {
            const prompts = await this.load();
            return prompts.some(prompt => prompt.id === id);
        }
        catch (error) {
            this.logger?.warn(`Error checking prompt existence: ${id}`, {
                error: error instanceof Error ? error.message : String(error)
            });
            return false;
        }
    }
    /**
     * Check if the directory exists
     */
    async directoryExists() {
        try {
            const stats = await fs.stat(this.path);
            return stats.isDirectory();
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Ensure the directory exists
     */
    async ensureDirectory() {
        try {
            await fs.mkdir(this.path, { recursive: true });
        }
        catch (error) {
            // Ignore error if directory already exists
            if (error?.code !== 'EEXIST') {
                throw error;
            }
        }
    }
}
//# sourceMappingURL=file.js.map