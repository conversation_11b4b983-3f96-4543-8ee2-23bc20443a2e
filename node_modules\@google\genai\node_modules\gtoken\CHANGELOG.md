# Changelog

[npm history][1]

[1]: https://www.npmjs.com/package/gtoken?activeTab=versions

## [7.1.0](https://github.com/googleapis/node-gtoken/compare/v7.0.1...v7.1.0) (2024-02-01)


### Features

* Enable Token Retries ([#481](https://github.com/googleapis/node-gtoken/issues/481)) ([ed9f91e](https://github.com/googleapis/node-gtoken/commit/ed9f91e4764744426de95fd2510b68ee53677514))

## [7.0.1](https://github.com/googleapis/node-gtoken/compare/v7.0.0...v7.0.1) (2023-07-12)


### Bug Fixes

* **deps:** Update gaxios to 6.0.0 ([#462](https://github.com/googleapis/node-gtoken/issues/462)) ([c05a2b3](https://github.com/googleapis/node-gtoken/commit/c05a2b35d1bb369fc54f80784d9361c0d6cbc2e7))

## [7.0.0](https://github.com/googleapis/node-gtoken/compare/v6.1.2...v7.0.0) (2023-07-11)


### ⚠ BREAKING CHANGES

* move to node 14 as minimum version ([#457](https://github.com/googleapis/node-gtoken/issues/457))
* remove support for conversion of *.p12 to *.pem ([#452](https://github.com/googleapis/node-gtoken/issues/452))

### Features

* Remove support for conversion of *.p12 to *.pem ([#452](https://github.com/googleapis/node-gtoken/issues/452)) ([522a96d](https://github.com/googleapis/node-gtoken/commit/522a96dd38ad5d486e9337f72efdf1a5523fded4))


### Miscellaneous Chores

* Move to node 14 as minimum version ([#457](https://github.com/googleapis/node-gtoken/issues/457)) ([429df81](https://github.com/googleapis/node-gtoken/commit/429df814cd4224d5eacce72cfe8e924e53cc7f30))

## [6.1.2](https://github.com/googleapis/node-gtoken/compare/v6.1.1...v6.1.2) (2022-08-23)


### Bug Fixes

* remove pip install statements ([#1546](https://github.com/googleapis/node-gtoken/issues/1546)) ([#440](https://github.com/googleapis/node-gtoken/issues/440)) ([6fb8562](https://github.com/googleapis/node-gtoken/commit/6fb856207b07112096c033adb6d3f0edf5e5093d))

## [6.1.1](https://github.com/googleapis/node-gtoken/compare/v6.1.0...v6.1.1) (2022-08-04)


### Bug Fixes

* **deps:** update gaxios to 5.0.1 ([#436](https://github.com/googleapis/node-gtoken/issues/436)) ([0d5d0e9](https://github.com/googleapis/node-gtoken/commit/0d5d0e9c6ec51911f1e44d97c02dc0cd791c7d05))

## [6.1.0](https://github.com/googleapis/node-gtoken/compare/v6.0.1...v6.1.0) (2022-06-28)


### Features

* allow customizing the http client ([#426](https://github.com/googleapis/node-gtoken/issues/426)) ([408ad04](https://github.com/googleapis/node-gtoken/commit/408ad048a2595313717bf427fc34aa29a6e7fb3c))

## [6.0.1](https://github.com/googleapis/node-gtoken/compare/v6.0.0...v6.0.1) (2022-06-07)


### Bug Fixes

* **deps:** update dependency google-p12-pem to v4 ([#430](https://github.com/googleapis/node-gtoken/issues/430)) ([bd0848b](https://github.com/googleapis/node-gtoken/commit/bd0848b15554742e2fd73b05073bd84e1aec2a3f))

## [6.0.0](https://github.com/googleapis/node-gtoken/compare/v5.3.2...v6.0.0) (2022-05-10)


### ⚠ BREAKING CHANGES

* update library to use Node 12 (#428)

### Build System

* update library to use Node 12 ([#428](https://github.com/googleapis/node-gtoken/issues/428)) ([288b662](https://github.com/googleapis/node-gtoken/commit/288b662d990ce53a6824ddc67c384253487fdc04))

### [5.3.2](https://github.com/googleapis/node-gtoken/compare/v5.3.1...v5.3.2) (2022-01-28)


### Bug Fixes

* upgrade google-p12-pem to 3.1.3 ([#413](https://github.com/googleapis/node-gtoken/issues/413)) ([c3cebaf](https://github.com/googleapis/node-gtoken/commit/c3cebaf620c62f57385804fe5d852ce3e6398dc1))

### [5.3.1](https://www.github.com/googleapis/node-gtoken/compare/v5.3.0...v5.3.1) (2021-08-11)


### Bug Fixes

* **build:** migrate to using main branch ([#392](https://www.github.com/googleapis/node-gtoken/issues/392)) ([992e3c5](https://www.github.com/googleapis/node-gtoken/commit/992e3c5e1520a376269b2476e5ce225f6ee96e2b))

## [5.3.0](https://www.github.com/googleapis/node-gtoken/compare/v5.2.1...v5.3.0) (2021-06-10)


### Features

* add `gcf-owl-bot[bot]` to `ignoreAuthors` ([#369](https://www.github.com/googleapis/node-gtoken/issues/369)) ([3142215](https://www.github.com/googleapis/node-gtoken/commit/3142215277ae2daa33f7fb3300f09ef438ded01f))

### [5.2.1](https://www.github.com/googleapis/node-gtoken/compare/v5.2.0...v5.2.1) (2021-01-26)


### Bug Fixes

* **deps:** remove dependency on mime ([#357](https://www.github.com/googleapis/node-gtoken/issues/357)) ([0a1e6b3](https://www.github.com/googleapis/node-gtoken/commit/0a1e6b32206364106631c0ca8cdd2e325de2af32))

## [5.2.0](https://www.github.com/googleapis/node-gtoken/compare/v5.1.0...v5.2.0) (2021-01-14)


### Features

* request new tokens before they expire ([#349](https://www.github.com/googleapis/node-gtoken/issues/349)) ([e84d9a3](https://www.github.com/googleapis/node-gtoken/commit/e84d9a31517c1449141708a0a2cddd9d0129fa95))

## [5.1.0](https://www.github.com/googleapis/node-gtoken/compare/v5.0.5...v5.1.0) (2020-11-14)


### Features

* dedupe concurrent requests ([#351](https://www.github.com/googleapis/node-gtoken/issues/351)) ([9001f1d](https://www.github.com/googleapis/node-gtoken/commit/9001f1d00931f480d40fa323c9b527beaef2254a))

### [5.0.5](https://www.github.com/googleapis/node-gtoken/compare/v5.0.4...v5.0.5) (2020-10-22)


### Bug Fixes

* **deps:** update dependency gaxios to v4 ([#342](https://www.github.com/googleapis/node-gtoken/issues/342)) ([7954a19](https://www.github.com/googleapis/node-gtoken/commit/7954a197e923469b031f0833a2016fa0378285b1))

### [5.0.4](https://www.github.com/googleapis/node-gtoken/compare/v5.0.3...v5.0.4) (2020-10-06)


### Bug Fixes

* **deps:** upgrade google-p12-pem ([#337](https://www.github.com/googleapis/node-gtoken/issues/337)) ([77a749d](https://www.github.com/googleapis/node-gtoken/commit/77a749d646c7ccc68e974f27827a9d538dfea784))

### [5.0.3](https://www.github.com/googleapis/node-gtoken/compare/v5.0.2...v5.0.3) (2020-07-27)


### Bug Fixes

* move gitattributes files to node templates ([#322](https://www.github.com/googleapis/node-gtoken/issues/322)) ([1d1786b](https://www.github.com/googleapis/node-gtoken/commit/1d1786b8915cd9a33577237ec6a6148a29e11a88))

### [5.0.2](https://www.github.com/googleapis/node-gtoken/compare/v5.0.1...v5.0.2) (2020-07-09)


### Bug Fixes

* typeo in nodejs .gitattribute ([#311](https://www.github.com/googleapis/node-gtoken/issues/311)) ([8e17b4c](https://www.github.com/googleapis/node-gtoken/commit/8e17b4c0757832b2d31178684a6b24e1759d9f76))

### [5.0.1](https://www.github.com/googleapis/node-gtoken/compare/v5.0.0...v5.0.1) (2020-04-13)


### Bug Fixes

* **deps:** update dependency gaxios to v3 ([#287](https://www.github.com/googleapis/node-gtoken/issues/287)) ([033731e](https://www.github.com/googleapis/node-gtoken/commit/033731e128fef0034b07b13183044e5060809418))
* **deps:** update dependency google-p12-pem to v3 ([#280](https://www.github.com/googleapis/node-gtoken/issues/280)) ([25121b0](https://www.github.com/googleapis/node-gtoken/commit/25121b00cc9a4d32854f36ea8bc4bbd2cb77afbb))
* apache license URL ([#468](https://www.github.com/googleapis/node-gtoken/issues/468)) ([#293](https://www.github.com/googleapis/node-gtoken/issues/293)) ([14a5bcd](https://www.github.com/googleapis/node-gtoken/commit/14a5bcd52d7b18d787c620451471e904784222d9))

## [5.0.0](https://www.github.com/googleapis/node-gtoken/compare/v4.1.4...v5.0.0) (2020-03-24)


### ⚠ BREAKING CHANGES

* drop Node 8 from engines (#284)
* typescript@3.7.x introduced breaking changes to compiled code

### Features

* drop Node 8 from engines ([#284](https://www.github.com/googleapis/node-gtoken/issues/284)) ([209e007](https://www.github.com/googleapis/node-gtoken/commit/209e00746116a82a3cf9acc158aff12a4971f3d0))


### Build System

* update gts and typescript ([#283](https://www.github.com/googleapis/node-gtoken/issues/283)) ([ff076dc](https://www.github.com/googleapis/node-gtoken/commit/ff076dcb3da229238e7bed28d739c48986652c78))

### [4.1.4](https://www.github.com/googleapis/node-gtoken/compare/v4.1.3...v4.1.4) (2020-01-06)


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([f1ae7b6](https://www.github.com/googleapis/node-gtoken/commit/f1ae7b64ead1c918546ae5bbe8546dfb4ecc788a))
* **deps:** update dependency jws to v4 ([#251](https://www.github.com/googleapis/node-gtoken/issues/251)) ([e13542f](https://www.github.com/googleapis/node-gtoken/commit/e13542f888a81ed3ced0023e9b78ed25264b1d1c))

### [4.1.3](https://www.github.com/googleapis/node-gtoken/compare/v4.1.2...v4.1.3) (2019-11-15)


### Bug Fixes

* **deps:** use typescript ~3.6.0 ([#246](https://www.github.com/googleapis/node-gtoken/issues/246)) ([5f725b7](https://www.github.com/googleapis/node-gtoken/commit/5f725b71f080e83058b1a23340acadc0c8704123))

### [4.1.2](https://www.github.com/googleapis/node-gtoken/compare/v4.1.1...v4.1.2) (2019-11-13)


### Bug Fixes

* **docs:** add jsdoc-region-tag plugin ([#242](https://www.github.com/googleapis/node-gtoken/issues/242)) ([994c5cc](https://www.github.com/googleapis/node-gtoken/commit/994c5ccf92731599aa63b84c29a9d5f6b1431cc5))

### [4.1.1](https://www.github.com/googleapis/node-gtoken/compare/v4.1.0...v4.1.1) (2019-10-31)


### Bug Fixes

* **deps:** update gaxios to 2.1.0 ([#238](https://www.github.com/googleapis/node-gtoken/issues/238)) ([bb12064](https://www.github.com/googleapis/node-gtoken/commit/bb1206420388399ef8992efe54c70bdb3fdcd965))

## [4.1.0](https://www.github.com/googleapis/node-gtoken/compare/v4.0.0...v4.1.0) (2019-09-24)


### Features

* allow upstream libraries to force token refresh ([#229](https://www.github.com/googleapis/node-gtoken/issues/229)) ([1fd4dd1](https://www.github.com/googleapis/node-gtoken/commit/1fd4dd1))

## [4.0.0](https://www.github.com/googleapis/node-gtoken/compare/v3.0.2...v4.0.0) (2019-07-09)


### ⚠ BREAKING CHANGES

* This commit creates multiple breaking changes. The `getToken()`
method previously returned `Promise<string>`, where the string was the
`access_token` returned from the response.  However, the `oauth2` endpoint could
return a variety of other fields, such as an `id_token` in special cases.

```js
const token = await getToken();
// old response: 'some.access.token'
// new response: { access_token: 'some.access.token'}
```

To further support this change, the `GoogleToken` class no longer exposes
a `token` variable.  It now exposes `rawToken`, `accessToken`, and `idToken`
fields which can be used to access the relevant values returned in the
response.

### Bug Fixes

* expose all fields from response ([#218](https://www.github.com/googleapis/node-gtoken/issues/218)) ([d463370](https://www.github.com/googleapis/node-gtoken/commit/d463370))

### [3.0.2](https://www.github.com/googleapis/node-gtoken/compare/v3.0.1...v3.0.2) (2019-06-26)


### Bug Fixes

* **docs:** make anchors work in jsdoc ([#215](https://www.github.com/googleapis/node-gtoken/issues/215)) ([c5f6c89](https://www.github.com/googleapis/node-gtoken/commit/c5f6c89))

### [3.0.1](https://www.github.com/googleapis/node-gtoken/compare/v3.0.0...v3.0.1) (2019-06-13)


### Bug Fixes

* **docs:** move to new client docs URL ([#212](https://www.github.com/googleapis/node-gtoken/issues/212)) ([b7a8c75](https://www.github.com/googleapis/node-gtoken/commit/b7a8c75))

## [3.0.0](https://www.github.com/googleapis/node-gtoken/compare/v2.3.3...v3.0.0) (2019-05-07)


### Bug Fixes

* **deps:** update dependency gaxios to v2 ([#191](https://www.github.com/googleapis/node-gtoken/issues/191)) ([da65ea7](https://www.github.com/googleapis/node-gtoken/commit/da65ea7))
* **deps:** update dependency google-p12-pem to v2 ([#196](https://www.github.com/googleapis/node-gtoken/issues/196)) ([b510f06](https://www.github.com/googleapis/node-gtoken/commit/b510f06))
* fs.readFile does not exist in browser ([#186](https://www.github.com/googleapis/node-gtoken/issues/186)) ([a16d8e7](https://www.github.com/googleapis/node-gtoken/commit/a16d8e7))


### Build System

* upgrade engines field to >=8.10.0 ([#194](https://www.github.com/googleapis/node-gtoken/issues/194)) ([ee4d6c8](https://www.github.com/googleapis/node-gtoken/commit/ee4d6c8))


### BREAKING CHANGES

* upgrade engines field to >=8.10.0 (#194)

## v2.3.3

03-13-2019 14:54 PDT

### Bug Fixes
- fix: propagate error message ([#173](https://github.com/google/node-gtoken/pull/173))

### Documentation
- docs: update links in contrib guide ([#171](https://github.com/google/node-gtoken/pull/171))
- docs: move CONTRIBUTING.md to root ([#166](https://github.com/google/node-gtoken/pull/166))
- docs: add lint/fix example to contributing guide ([#164](https://github.com/google/node-gtoken/pull/164))

### Internal / Testing Changes
- build: Add docuploader credentials to node publish jobs ([#176](https://github.com/google/node-gtoken/pull/176))
- build: use node10 to run samples-test, system-test etc ([#175](https://github.com/google/node-gtoken/pull/175))
- build: update release configuration
- chore(deps): update dependency mocha to v6
- build: use linkinator for docs test ([#170](https://github.com/google/node-gtoken/pull/170))
- build: create docs test npm scripts ([#169](https://github.com/google/node-gtoken/pull/169))
- build: test using @grpc/grpc-js in CI ([#168](https://github.com/google/node-gtoken/pull/168))
- build: ignore googleapis.com in doc link check ([#162](https://github.com/google/node-gtoken/pull/162))
- build: check for 404s on all docs

## v2.3.2

01-09-2019 13:40 PST

### Documentation
- docs: generate docs with compodoc ([#154](https://github.com/googleapis/node-gtoken/pull/154))
- docs: fix up the readme ([#153](https://github.com/googleapis/node-gtoken/pull/153))

### Internal / Testing Changes
- build: Re-generated  to pick up changes in the API or client library generator. ([#158](https://github.com/googleapis/node-gtoken/pull/158))
- build: check broken links in generated docs ([#152](https://github.com/googleapis/node-gtoken/pull/152))
- fix: add a system test and get it passing ([#150](https://github.com/googleapis/node-gtoken/pull/150))
- chore(build): inject yoshi automation key ([#149](https://github.com/googleapis/node-gtoken/pull/149))

## v2.3.1

12-10-2018 15:28 PST

### Dependencies
- fix(deps): update dependency pify to v4 ([#87](https://github.com/google/node-gtoken/pull/87))
- fix(deps): use gaxios for http requests ([#125](https://github.com/google/node-gtoken/pull/125))

### Internal / Testing Changes
- build: add Kokoro configs for autorelease ([#143](https://github.com/google/node-gtoken/pull/143))
- chore: always nyc report before calling codecov ([#141](https://github.com/google/node-gtoken/pull/141))
- chore: nyc ignore build/test by default ([#140](https://github.com/google/node-gtoken/pull/140))
- chore: update synth metadata and templates ([#138](https://github.com/google/node-gtoken/pull/138))
- fix(build): fix system key decryption ([#133](https://github.com/google/node-gtoken/pull/133))
- chore(deps): update dependency typescript to ~3.2.0 ([#132](https://github.com/google/node-gtoken/pull/132))
- chore: add a synth.metadata
- chore(deps): update dependency gts to ^0.9.0 ([#127](https://github.com/google/node-gtoken/pull/127))
- chore: update eslintignore config ([#126](https://github.com/google/node-gtoken/pull/126))
- chore: use latest npm on Windows ([#124](https://github.com/google/node-gtoken/pull/124))
- chore: update CircleCI config ([#123](https://github.com/google/node-gtoken/pull/123))
- chore: include build in eslintignore ([#120](https://github.com/google/node-gtoken/pull/120))
- chore: update issue templates ([#116](https://github.com/google/node-gtoken/pull/116))
- chore: remove old issue template ([#114](https://github.com/google/node-gtoken/pull/114))
- build: run tests on node11 ([#113](https://github.com/google/node-gtoken/pull/113))
- chore(deps): update dependency nock to v10 ([#111](https://github.com/google/node-gtoken/pull/111))
- chores(build): do not collect sponge.xml from windows builds ([#112](https://github.com/google/node-gtoken/pull/112))
- chore(deps): update dependency typescript to ~3.1.0 ([#110](https://github.com/google/node-gtoken/pull/110))
- chores(build): run codecov on continuous builds ([#109](https://github.com/google/node-gtoken/pull/109))
- chore: update new issue template ([#108](https://github.com/google/node-gtoken/pull/108))
- chore: update CI config ([#105](https://github.com/google/node-gtoken/pull/105))
- Update kokoro config ([#103](https://github.com/google/node-gtoken/pull/103))
- Update CI config ([#101](https://github.com/google/node-gtoken/pull/101))
- Don't publish sourcemaps ([#99](https://github.com/google/node-gtoken/pull/99))
- Update kokoro config ([#97](https://github.com/google/node-gtoken/pull/97))
- test: remove appveyor config ([#96](https://github.com/google/node-gtoken/pull/96))
- Update CI config ([#95](https://github.com/google/node-gtoken/pull/95))
- Enable prefer-const in the eslint config ([#94](https://github.com/google/node-gtoken/pull/94))
- Enable no-var in eslint ([#93](https://github.com/google/node-gtoken/pull/93))
- Update CI config ([#92](https://github.com/google/node-gtoken/pull/92))
- Add synth and update CI config ([#91](https://github.com/google/node-gtoken/pull/91))
- Retry npm install in CI ([#90](https://github.com/google/node-gtoken/pull/90))
- chore(deps): update dependency nyc to v13 ([#88](https://github.com/google/node-gtoken/pull/88))
- chore: ignore package-log.json ([#86](https://github.com/google/node-gtoken/pull/86))
- chore: update renovate config ([#83](https://github.com/google/node-gtoken/pull/83))
- chore(deps): lock file maintenance ([#85](https://github.com/google/node-gtoken/pull/85))
- chore: remove greenkeeper badge ([#82](https://github.com/google/node-gtoken/pull/82))
- test: throw on deprecation ([#81](https://github.com/google/node-gtoken/pull/81))
- chore(deps): update dependency typescript to v3 ([#80](https://github.com/google/node-gtoken/pull/80))
- chore: move mocha options to mocha.opts ([#78](https://github.com/google/node-gtoken/pull/78))
- chore(deps): lock file maintenance ([#79](https://github.com/google/node-gtoken/pull/79))
- test: use strictEqual in tests ([#76](https://github.com/google/node-gtoken/pull/76))
- chore(deps): lock file maintenance ([#77](https://github.com/google/node-gtoken/pull/77))
- chore(deps): update dependency typescript to ~2.9.0 ([#75](https://github.com/google/node-gtoken/pull/75))
- chore: Configure Renovate ([#74](https://github.com/google/node-gtoken/pull/74))
- Update gts to the latest version 🚀 ([#73](https://github.com/google/node-gtoken/pull/73))
- Add Code of Conduct
- build: start testing against Node 10 ([#69](https://github.com/google/node-gtoken/pull/69))
- chore(package): update nyc to version 12.0.2 ([#67](https://github.com/google/node-gtoken/pull/67))
- chore(package): update @types/node to version 10.0.3 ([#65](https://github.com/google/node-gtoken/pull/65))

### 2.0.0
New features:
- API now supports callback and promise based workflows

Breaking changes:
- `GoogleToken` is now a class type, and must be instantiated.
- `GoogleToken.expires_at` renamed to `GoogleToken.expiresAt`
- `GoogleToken.raw_token` renamed to `GoogleToken.rawToken`
- `GoogleToken.token_expires` renamed to `GoogleToken.tokenExpires`
