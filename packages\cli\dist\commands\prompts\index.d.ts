/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Arguments } from 'yargs';
/**
 * Prompt management commands
 */
interface PromptCommandArgs {
    action: 'list' | 'show' | 'create' | 'edit' | 'delete' | 'test';
    id?: string;
    category?: string;
    tag?: string;
    search?: string;
    file?: string;
    output?: string;
}
export declare const promptCommand: {
    command: string;
    describe: string;
    builder: (yargs: any) => any;
    handler: (argv: Arguments<PromptCommandArgs>) => Promise<void>;
};
export {};
//# sourceMappingURL=index.d.ts.map