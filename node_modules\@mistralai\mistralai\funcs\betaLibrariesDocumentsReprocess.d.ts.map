{"version": 3, "file": "betaLibrariesDocumentsReprocess.d.ts", "sourceRoot": "", "sources": ["../src/funcs/betaLibrariesDocumentsReprocess.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAKzC,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAGhD,OAAO,EACL,eAAe,EACf,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACtB,MAAM,sCAAsC,CAAC;AAC9C,OAAO,KAAK,MAAM,MAAM,2BAA2B,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,wCAAwC,CAAC;AAC5E,OAAO,KAAK,UAAU,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAW,UAAU,EAAE,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC;;;;;GAKG;AACH,wBAAgB,+BAA+B,CAC7C,MAAM,EAAE,WAAW,EACnB,OAAO,EAAE,UAAU,CAAC,oCAAoC,EACxD,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CACX,MAAM,CACJ,IAAI,EACF,MAAM,CAAC,mBAAmB,GAC1B,QAAQ,GACR,kBAAkB,GAClB,qBAAqB,GACrB,mBAAmB,GACnB,mBAAmB,GACnB,mBAAmB,GACnB,eAAe,CAClB,CACF,CAMA"}