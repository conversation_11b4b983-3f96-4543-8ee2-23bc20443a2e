{"version": 3, "file": "manager.d.ts", "sourceRoot": "", "sources": ["../../src/ai/manager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EACV,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,UAAU,EACV,qBAAqB,EACrB,sBAAsB,EACtB,mBAAmB,EACnB,gBAAgB,EAChB,iBAAiB,EACjB,SAAS,EACT,WAAW,EACX,WAAW,EACX,MAAM,EACN,aAAa,EACb,aAAa,EACd,MAAM,wBAAwB,CAAC;AAIhC;;GAEG;AACH,qBAAa,SAAS;IACpB,OAAO,CAAC,OAAO,CAAkB;IACjC,OAAO,CAAC,QAAQ,CAAmB;IACnC,OAAO,CAAC,iBAAiB,CAAC,CAAa;IACvC,OAAO,CAAC,MAAM,CAAqB;IACnC,OAAO,CAAC,aAAa,CAAC,CAAgB;gBAE1B,MAAM,CAAC,EAAE,MAAM;IAM3B;;OAEG;IACI,gBAAgB,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI;IAK3D;;OAEG;IACU,UAAU,CACrB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,EACvC,eAAe,CAAC,EAAE,MAAM,GACvB,WAAW,CAAC,IAAI,CAAC;IAkDpB;;OAEG;IACI,kBAAkB,IAAI,QAAQ,GAAG,SAAS;IAOjD;;OAEG;IACI,WAAW,CAAC,EAAE,EAAE,UAAU,GAAG,QAAQ,GAAG,SAAS;IAIxD;;OAEG;IACI,qBAAqB,IAAI,QAAQ,EAAE;IAI1C;;OAEG;IACI,kBAAkB,CAAC,IAAI,EAAE,YAAY,GAAG,QAAQ,EAAE;IAIzD;;OAEG;IACI,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS;IAI9D;;OAEG;IACU,oBAAoB,CAC/B,OAAO,EAAE,qBAAqB,EAC9B,UAAU,CAAC,EAAE,UAAU,EACvB,OAAO,CAAC,EAAE,aAAa,GACtB,WAAW,CAAC,sBAAsB,CAAC;IAyBtC;;OAEG;IACU,0BAA0B,CACrC,OAAO,EAAE,qBAAqB,EAC9B,UAAU,CAAC,EAAE,UAAU,EACvB,OAAO,CAAC,EAAE,aAAa,GACtB,WAAW,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;IAyBlD;;OAEG;IACU,eAAe,CAC1B,OAAO,EAAE,gBAAgB,EACzB,UAAU,CAAC,EAAE,UAAU,GACtB,WAAW,CAAC,iBAAiB,CAAC;IAqBjC;;OAEG;IACU,WAAW,CACtB,KAAK,EAAE,MAAM,GAAG,WAAW,EAAE,EAC7B,OAAO,CAAC,EAAE,MAAM,EAChB,UAAU,CAAC,EAAE,UAAU,GACtB,WAAW,CAAC,MAAM,CAAC;IAetB;;OAEG;IACU,kBAAkB,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;IAwB3D;;OAEG;IACI,QAAQ;;;;;IAIf;;OAEG;IACU,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAKrC;;OAEG;IACH,OAAO,CAAC,cAAc;IAqBtB;;OAEG;YACW,kBAAkB;CAuDjC"}