import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Get Fine Tuning Job
 *
 * @remarks
 * Get a fine-tuned job details by its UUID.
 */
export declare function fineTuningJobsGet(client: MistralCore, request: operations.JobsApiRoutesFineTuningGetFineTuningJobRequest, options?: RequestOptions): APIPromise<Result<operations.JobsApiRoutesFineTuningGetFineTuningJobResponse, SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=fineTuningJobsGet.d.ts.map