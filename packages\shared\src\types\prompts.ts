/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { JsonValue } from './common.js';

/**
 * Prompt template variable types
 */
export type PromptVariable = string | number | boolean | JsonValue;

/**
 * Prompt template context
 */
export interface PromptContext {
  // Project context
  projectName?: string;
  projectType?: string;
  language?: string;
  framework?: string;

  // File context
  fileName?: string;
  fileExtension?: string;
  filePath?: string;

  // User context
  userName?: string;
  userRole?: string;

  // Session context
  currentTask?: string;
  sessionId?: string;
  timestamp?: number;

  // Built-in variables
  date?: string;
  time?: string;
  random?: string;

  // Custom variables
  [key: string]: PromptVariable | undefined;
}

/**
 * Prompt template metadata
 */
export interface PromptMetadata {
  name: string;
  description: string;
  version: string;
  author?: string;
  tags?: string[];
  category?: PromptCategory;
  language?: string;
  createdAt?: number;
  updatedAt?: number;
}

/**
 * Prompt categories for organization
 */
export type PromptCategory = 
  | 'role-based'
  | 'task-specific'
  | 'domain-specific'
  | 'communication-style'
  | 'custom';

/**
 * Prompt template definition
 */
export interface PromptTemplate {
  id: string;
  metadata: PromptMetadata;
  content: string;
  variables?: string[];
  conditions?: PromptCondition[];
  extends?: string; // Base template to extend
  priority?: number; // For conflict resolution
}

/**
 * Conditional logic for prompts
 */
export interface PromptCondition {
  variable: string;
  operator: 'equals' | 'not-equals' | 'contains' | 'not-contains' | 'exists' | 'not-exists';
  value?: PromptVariable;
}

/**
 * Rendered prompt result
 */
export interface RenderedPrompt {
  id: string;
  content: string;
  metadata: PromptMetadata;
  context: PromptContext;
  variables: Record<string, PromptVariable>;
}

/**
 * Prompt loading source
 */
export type PromptSource = 'built-in' | 'global' | 'project' | 'session';

/**
 * Prompt loader configuration
 */
export interface PromptLoaderConfig {
  source: PromptSource;
  path?: string;
  enabled: boolean;
  priority: number;
}

/**
 * Prompt manager configuration
 */
export interface PromptManagerConfig {
  defaultPrompt?: string;
  loaders: PromptLoaderConfig[];
  templateEngine?: 'mustache' | 'handlebars';
  cacheEnabled?: boolean;
  cacheTtl?: number;
}

/**
 * Prompt selection criteria
 */
export interface PromptSelector {
  id?: string;
  category?: PromptCategory;
  tags?: string[];
  context?: Partial<PromptContext>;
}

/**
 * Prompt validation result
 */
export interface PromptValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Built-in prompt identifiers
 */
export type BuiltInPromptId = 
  // Role-based prompts
  | 'developer-general'
  | 'code-reviewer'
  | 'debugger'
  | 'architect'
  | 'mentor'
  | 'analyst'
  
  // Task-specific prompts
  | 'documentation'
  | 'testing'
  | 'refactoring'
  | 'optimization'
  | 'security'
  | 'deployment'
  
  // Communication style prompts
  | 'concise'
  | 'detailed'
  | 'step-by-step'
  | 'creative'
  | 'formal'
  | 'casual'
  
  // Domain-specific prompts
  | 'web-development'
  | 'mobile-development'
  | 'data-science'
  | 'devops'
  | 'machine-learning';

/**
 * Prompt library interface
 */
export interface PromptLibrary {
  getPrompt(id: string): PromptTemplate | undefined;
  getAllPrompts(): PromptTemplate[];
  getPromptsByCategory(category: PromptCategory): PromptTemplate[];
  getPromptsByTag(tag: string): PromptTemplate[];
  searchPrompts(query: string): PromptTemplate[];
}

/**
 * Prompt renderer interface
 */
export interface PromptRenderer {
  render(template: PromptTemplate, context: PromptContext): Promise<RenderedPrompt>;
  validate(template: PromptTemplate): PromptValidationResult;
  extractVariables(content: string): string[];
}

/**
 * Prompt manager interface
 */
export interface PromptManager {
  initialize(config: PromptManagerConfig): Promise<void>;
  getPrompt(selector: PromptSelector): Promise<PromptTemplate | undefined>;
  renderPrompt(selector: PromptSelector, context: PromptContext): Promise<RenderedPrompt | undefined>;
  addPrompt(template: PromptTemplate): Promise<void>;
  removePrompt(id: string): Promise<void>;
  updatePrompt(id: string, template: Partial<PromptTemplate>): Promise<void>;
  listPrompts(category?: PromptCategory): Promise<PromptTemplate[]>;
  searchPrompts(query: string): Promise<PromptTemplate[]>;
  validatePrompt(template: PromptTemplate): PromptValidationResult;
  getContext(): PromptContext;
  updateContext(context: Partial<PromptContext>): void;
}

/**
 * Prompt loader interface
 */
export interface PromptLoader {
  readonly source: PromptSource;
  readonly priority: number;
  load(): Promise<PromptTemplate[]>;
  save(templates: PromptTemplate[]): Promise<void>;
  exists(id: string): Promise<boolean>;
}
