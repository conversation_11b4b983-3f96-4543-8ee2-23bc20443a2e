/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { PromptTemplate, PromptLoader, PromptSource, Logger } from '@inkbytefo/s647-shared';
/**
 * File-based prompt loader
 * Loads prompts from JSON files in a directory
 */
export declare class FilePromptLoader implements PromptLoader {
    readonly source: PromptSource;
    readonly priority: number;
    private readonly path;
    private logger;
    constructor(source: PromptSource, priority: number, path: string, logger?: Logger);
    /**
     * Load prompts from the configured directory
     */
    load(): Promise<PromptTemplate[]>;
    /**
     * Save prompts to the configured directory
     */
    save(templates: PromptTemplate[]): Promise<void>;
    /**
     * Check if a prompt exists
     */
    exists(id: string): Promise<boolean>;
    /**
     * Check if the directory exists
     */
    private directoryExists;
    /**
     * Ensure the directory exists
     */
    private ensureDirectory;
}
//# sourceMappingURL=file.d.ts.map