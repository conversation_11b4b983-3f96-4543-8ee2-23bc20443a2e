{"version": 3, "file": "betaLibrariesDocumentsUpload.d.ts", "sourceRoot": "", "sources": ["../src/funcs/betaLibrariesDocumentsUpload.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAMzC,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAGhD,OAAO,KAAK,UAAU,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EACL,eAAe,EACf,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACtB,MAAM,sCAAsC,CAAC;AAC9C,OAAO,KAAK,MAAM,MAAM,2BAA2B,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,wCAAwC,CAAC;AAC5E,OAAO,KAAK,UAAU,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAW,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAGxC;;;;;GAKG;AACH,wBAAgB,4BAA4B,CAC1C,MAAM,EAAE,WAAW,EACnB,OAAO,EAAE,UAAU,CAAC,iCAAiC,EACrD,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CACX,MAAM,CACJ,UAAU,CAAC,WAAW,EACpB,MAAM,CAAC,mBAAmB,GAC1B,QAAQ,GACR,kBAAkB,GAClB,qBAAqB,GACrB,mBAAmB,GACnB,mBAAmB,GACnB,mBAAmB,GACnB,eAAe,CAClB,CACF,CAMA"}