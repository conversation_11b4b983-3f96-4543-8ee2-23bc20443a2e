/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { PromptTemplateSchema } from '@inkbytefo/s647-shared';
import { PromptLoaderRegistry } from './loaders/registry.js';
import { PromptRenderer } from './renderers/mustache.js';
import { BuiltInPromptLibrary } from './templates/built-in.js';
/**
 * Prompt manager implementation
 * Handles loading, rendering, and management of system prompts
 */
export class PromptManagerImpl {
    loaderRegistry;
    renderer;
    builtInLibrary;
    context = {};
    cache = new Map();
    config;
    logger;
    constructor(logger) {
        this.logger = logger;
        this.loaderRegistry = new PromptLoaderRegistry(logger);
        this.renderer = new PromptRenderer();
        this.builtInLibrary = new BuiltInPromptLibrary();
    }
    /**
     * Initialize the prompt manager
     */
    async initialize(config) {
        this.config = config;
        this.logger?.info('Initializing Prompt Manager', { config });
        // Initialize loader registry
        await this.loaderRegistry.initialize(config.loaders);
        // Load all prompts
        await this.loadAllPrompts();
        this.logger?.info('Prompt Manager initialized successfully');
    }
    /**
     * Get a prompt by selector
     */
    async getPrompt(selector) {
        this.logger?.debug('Getting prompt', { selector });
        // Try to find by ID first
        if (selector.id) {
            const cached = this.cache.get(selector.id);
            if (cached) {
                return cached;
            }
            // Check built-in library
            const builtIn = this.builtInLibrary.getPrompt(selector.id);
            if (builtIn) {
                return builtIn;
            }
        }
        // Search by criteria
        const allPrompts = await this.getAllPrompts();
        return this.findBestMatch(allPrompts, selector);
    }
    /**
     * Render a prompt with context
     */
    async renderPrompt(selector, context) {
        const template = await this.getPrompt(selector);
        if (!template) {
            this.logger?.warn('Prompt not found', { selector });
            return undefined;
        }
        // Merge context
        const mergedContext = { ...this.context, ...context };
        try {
            const rendered = await this.renderer.render(template, mergedContext);
            this.logger?.debug('Prompt rendered successfully', {
                templateId: template.id,
                contextKeys: Object.keys(mergedContext)
            });
            return rendered;
        }
        catch (error) {
            this.logger?.error('Failed to render prompt', {
                templateId: template.id,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Add a new prompt
     */
    async addPrompt(template) {
        const validation = this.validatePrompt(template);
        if (!validation.valid) {
            throw new Error(`Invalid prompt template: ${validation.errors.join(', ')}`);
        }
        this.cache.set(template.id, template);
        this.logger?.info('Prompt added', { id: template.id });
    }
    /**
     * Remove a prompt
     */
    async removePrompt(id) {
        this.cache.delete(id);
        this.logger?.info('Prompt removed', { id });
    }
    /**
     * Update a prompt
     */
    async updatePrompt(id, template) {
        const existing = this.cache.get(id);
        if (!existing) {
            throw new Error(`Prompt not found: ${id}`);
        }
        const updated = { ...existing, ...template, id };
        const validation = this.validatePrompt(updated);
        if (!validation.valid) {
            throw new Error(`Invalid prompt template: ${validation.errors.join(', ')}`);
        }
        this.cache.set(id, updated);
        this.logger?.info('Prompt updated', { id });
    }
    /**
     * List prompts by category
     */
    async listPrompts(category) {
        const allPrompts = await this.getAllPrompts();
        if (!category) {
            return allPrompts;
        }
        return allPrompts.filter(prompt => prompt.metadata.category === category);
    }
    /**
     * Search prompts by query
     */
    async searchPrompts(query) {
        const allPrompts = await this.getAllPrompts();
        const lowerQuery = query.toLowerCase();
        return allPrompts.filter(prompt => prompt.metadata.name.toLowerCase().includes(lowerQuery) ||
            prompt.metadata.description.toLowerCase().includes(lowerQuery) ||
            prompt.metadata.tags?.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
            prompt.content.toLowerCase().includes(lowerQuery));
    }
    /**
     * Validate a prompt template
     */
    validatePrompt(template) {
        try {
            PromptTemplateSchema.parse(template);
            // Additional validation
            const errors = [];
            const warnings = [];
            // Check for required variables
            const variables = this.renderer.extractVariables(template.content);
            if (template.variables) {
                const missingVars = template.variables.filter(v => !variables.includes(v));
                if (missingVars.length > 0) {
                    warnings.push(`Declared variables not used: ${missingVars.join(', ')}`);
                }
            }
            // Check for undefined variables
            const undeclaredVars = variables.filter((v) => !template.variables?.includes(v));
            if (undeclaredVars.length > 0) {
                warnings.push(`Undeclared variables used: ${undeclaredVars.join(', ')}`);
            }
            return {
                valid: errors.length === 0,
                errors,
                warnings,
            };
        }
        catch (error) {
            return {
                valid: false,
                errors: [error instanceof Error ? error.message : String(error)],
                warnings: [],
            };
        }
    }
    /**
     * Get current context
     */
    getContext() {
        return { ...this.context };
    }
    /**
     * Update context
     */
    updateContext(context) {
        // Filter out undefined values to satisfy exactOptionalPropertyTypes
        const filteredContext = {};
        for (const [key, value] of Object.entries(context)) {
            if (value !== undefined) {
                filteredContext[key] = value;
            }
        }
        this.context = { ...this.context, ...filteredContext };
        this.logger?.debug('Context updated', { context: Object.keys(context) });
    }
    /**
     * Load all prompts from all sources
     */
    async loadAllPrompts() {
        try {
            const prompts = await this.loaderRegistry.loadAllPrompts();
            // Clear cache and reload
            this.cache.clear();
            for (const prompt of prompts) {
                this.cache.set(prompt.id, prompt);
            }
            this.logger?.info('Loaded prompts', { count: prompts.length });
        }
        catch (error) {
            this.logger?.error('Failed to load prompts', {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Get all prompts from all sources
     */
    async getAllPrompts() {
        const cached = Array.from(this.cache.values());
        const builtIn = this.builtInLibrary.getAllPrompts();
        // Merge and deduplicate (cache takes priority)
        const allPrompts = [...cached];
        const cachedIds = new Set(cached.map(p => p.id));
        for (const prompt of builtIn) {
            if (!cachedIds.has(prompt.id)) {
                allPrompts.push(prompt);
            }
        }
        return allPrompts;
    }
    /**
     * Find the best matching prompt for a selector
     */
    findBestMatch(prompts, selector) {
        let candidates = prompts;
        // Filter by category
        if (selector.category) {
            candidates = candidates.filter(p => p.metadata.category === selector.category);
        }
        // Filter by tags
        if (selector.tags && selector.tags.length > 0) {
            candidates = candidates.filter(p => selector.tags.some(tag => p.metadata.tags?.includes(tag)));
        }
        // Filter by context (basic matching)
        if (selector.context) {
            candidates = candidates.filter(p => this.matchesContext(p, selector.context));
        }
        // Sort by priority and return the best match
        candidates.sort((a, b) => (b.priority || 0) - (a.priority || 0));
        return candidates[0];
    }
    /**
     * Check if a prompt matches the given context
     */
    matchesContext(prompt, context) {
        if (!prompt.conditions) {
            return true;
        }
        return prompt.conditions.every(condition => {
            const value = context[condition.variable];
            switch (condition.operator) {
                case 'equals':
                    return value === condition.value;
                case 'not-equals':
                    return value !== condition.value;
                case 'contains':
                    return typeof value === 'string' && typeof condition.value === 'string'
                        && value.includes(condition.value);
                case 'not-contains':
                    return typeof value === 'string' && typeof condition.value === 'string'
                        && !value.includes(condition.value);
                case 'exists':
                    return value !== undefined && value !== null;
                case 'not-exists':
                    return value === undefined || value === null;
                default:
                    return true;
            }
        });
    }
}
//# sourceMappingURL=manager.js.map