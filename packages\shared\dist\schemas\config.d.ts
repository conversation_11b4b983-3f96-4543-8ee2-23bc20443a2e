/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
/**
 * Configuration schemas using Zod for runtime validation
 */
export declare const ProviderConfigSchema: z.ZodObject<{
    type: z.ZodEnum<["openai", "anthropic", "google", "mistral", "openrouter", "custom", "local"]>;
    apiKey: z.ZodOptional<z.ZodString>;
    baseUrl: z.ZodOptional<z.ZodString>;
    timeout: z.ZodOptional<z.ZodNumber>;
    retries: z.ZodOptional<z.ZodNumber>;
    headers: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
    proxy: z.ZodOptional<z.ZodString>;
    enabled: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    type: "custom" | "openai" | "anthropic" | "google" | "mistral" | "openrouter" | "local";
    enabled?: boolean | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    timeout?: number | undefined;
    retries?: number | undefined;
    headers?: Record<string, string> | undefined;
    proxy?: string | undefined;
}, {
    type: "custom" | "openai" | "anthropic" | "google" | "mistral" | "openrouter" | "local";
    enabled?: boolean | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    timeout?: number | undefined;
    retries?: number | undefined;
    headers?: Record<string, string> | undefined;
    proxy?: string | undefined;
}>;
export declare const ToolConfigSchema: z.ZodObject<{
    enabled: z.ZodBoolean;
    timeout: z.ZodOptional<z.ZodNumber>;
    retries: z.ZodOptional<z.ZodNumber>;
    options: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
}, "strip", z.ZodTypeAny, {
    enabled: boolean;
    options?: Record<string, unknown> | undefined;
    timeout?: number | undefined;
    retries?: number | undefined;
}, {
    enabled: boolean;
    options?: Record<string, unknown> | undefined;
    timeout?: number | undefined;
    retries?: number | undefined;
}>;
export declare const UIConfigSchema: z.ZodObject<{
    theme: z.ZodEnum<["light", "dark", "auto"]>;
    animations: z.ZodBoolean;
    verbose: z.ZodBoolean;
    showTimestamps: z.ZodOptional<z.ZodBoolean>;
    showTokenCount: z.ZodOptional<z.ZodBoolean>;
    autoScroll: z.ZodOptional<z.ZodBoolean>;
    maxHistoryLines: z.ZodOptional<z.ZodNumber>;
    colors: z.ZodOptional<z.ZodObject<{
        primary: z.ZodString;
        secondary: z.ZodString;
        success: z.ZodString;
        warning: z.ZodString;
        error: z.ZodString;
        info: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        error: string;
        info: string;
    }, {
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        error: string;
        info: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    theme: "light" | "dark" | "auto";
    animations: boolean;
    verbose: boolean;
    showTimestamps?: boolean | undefined;
    showTokenCount?: boolean | undefined;
    autoScroll?: boolean | undefined;
    maxHistoryLines?: number | undefined;
    colors?: {
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        error: string;
        info: string;
    } | undefined;
}, {
    theme: "light" | "dark" | "auto";
    animations: boolean;
    verbose: boolean;
    showTimestamps?: boolean | undefined;
    showTokenCount?: boolean | undefined;
    autoScroll?: boolean | undefined;
    maxHistoryLines?: number | undefined;
    colors?: {
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        error: string;
        info: string;
    } | undefined;
}>;
export declare const LoggingConfigSchema: z.ZodObject<{
    level: z.ZodEnum<["debug", "info", "warn", "error"]>;
    format: z.ZodEnum<["text", "json"]>;
    output: z.ZodEnum<["console", "file", "both"]>;
    file: z.ZodOptional<z.ZodObject<{
        path: z.ZodString;
        maxSize: z.ZodString;
        maxFiles: z.ZodNumber;
        compress: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        path: string;
        maxSize: string;
        maxFiles: number;
        compress: boolean;
    }, {
        path: string;
        maxSize: string;
        maxFiles: number;
        compress: boolean;
    }>>;
    structured: z.ZodBoolean;
    includeTimestamp: z.ZodBoolean;
    includeLevel: z.ZodBoolean;
    includeSource: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    level: "error" | "info" | "debug" | "warn";
    format: "text" | "json";
    output: "console" | "file" | "both";
    structured: boolean;
    includeTimestamp: boolean;
    includeLevel: boolean;
    includeSource: boolean;
    file?: {
        path: string;
        maxSize: string;
        maxFiles: number;
        compress: boolean;
    } | undefined;
}, {
    level: "error" | "info" | "debug" | "warn";
    format: "text" | "json";
    output: "console" | "file" | "both";
    structured: boolean;
    includeTimestamp: boolean;
    includeLevel: boolean;
    includeSource: boolean;
    file?: {
        path: string;
        maxSize: string;
        maxFiles: number;
        compress: boolean;
    } | undefined;
}>;
export declare const ConfigurationSchema: z.ZodObject<{
    version: z.ZodString;
    environment: z.ZodEnum<["development", "production", "test"]>;
    profile: z.ZodOptional<z.ZodString>;
    defaultProvider: z.ZodString;
    providers: z.ZodRecord<z.ZodString, z.ZodObject<{
        type: z.ZodEnum<["openai", "anthropic", "google", "mistral", "openrouter", "custom", "local"]>;
        apiKey: z.ZodOptional<z.ZodString>;
        baseUrl: z.ZodOptional<z.ZodString>;
        timeout: z.ZodOptional<z.ZodNumber>;
        retries: z.ZodOptional<z.ZodNumber>;
        headers: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
        proxy: z.ZodOptional<z.ZodString>;
        enabled: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        type: "custom" | "openai" | "anthropic" | "google" | "mistral" | "openrouter" | "local";
        enabled?: boolean | undefined;
        apiKey?: string | undefined;
        baseUrl?: string | undefined;
        timeout?: number | undefined;
        retries?: number | undefined;
        headers?: Record<string, string> | undefined;
        proxy?: string | undefined;
    }, {
        type: "custom" | "openai" | "anthropic" | "google" | "mistral" | "openrouter" | "local";
        enabled?: boolean | undefined;
        apiKey?: string | undefined;
        baseUrl?: string | undefined;
        timeout?: number | undefined;
        retries?: number | undefined;
        headers?: Record<string, string> | undefined;
        proxy?: string | undefined;
    }>>;
    tools: z.ZodObject<{
        enabled: z.ZodArray<z.ZodString, "many">;
        file: z.ZodOptional<z.ZodObject<{
            enabled: z.ZodBoolean;
            timeout: z.ZodOptional<z.ZodNumber>;
            retries: z.ZodOptional<z.ZodNumber>;
            options: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }>>;
        git: z.ZodOptional<z.ZodObject<{
            enabled: z.ZodBoolean;
            timeout: z.ZodOptional<z.ZodNumber>;
            retries: z.ZodOptional<z.ZodNumber>;
            options: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }>>;
        web: z.ZodOptional<z.ZodObject<{
            enabled: z.ZodBoolean;
            timeout: z.ZodOptional<z.ZodNumber>;
            retries: z.ZodOptional<z.ZodNumber>;
            options: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }>>;
        shell: z.ZodOptional<z.ZodObject<{
            enabled: z.ZodBoolean;
            timeout: z.ZodOptional<z.ZodNumber>;
            retries: z.ZodOptional<z.ZodNumber>;
            options: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }>>;
        memory: z.ZodOptional<z.ZodObject<{
            enabled: z.ZodBoolean;
            timeout: z.ZodOptional<z.ZodNumber>;
            retries: z.ZodOptional<z.ZodNumber>;
            options: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }, {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        enabled: string[];
        file?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        git?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        web?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        shell?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        memory?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
    }, {
        enabled: string[];
        file?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        git?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        web?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        shell?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        memory?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
    }>;
    prompts: z.ZodObject<{
        defaultPrompt: z.ZodOptional<z.ZodString>;
        loaders: z.ZodArray<z.ZodObject<{
            source: z.ZodEnum<["built-in", "global", "project", "session"]>;
            path: z.ZodOptional<z.ZodString>;
            enabled: z.ZodBoolean;
            priority: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            priority: number;
            source: "built-in" | "global" | "project" | "session";
            enabled: boolean;
            path?: string | undefined;
        }, {
            priority: number;
            source: "built-in" | "global" | "project" | "session";
            enabled: boolean;
            path?: string | undefined;
        }>, "many">;
        templateEngine: z.ZodOptional<z.ZodEnum<["mustache", "handlebars"]>>;
        cacheEnabled: z.ZodOptional<z.ZodBoolean>;
        cacheTtl: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        loaders: {
            priority: number;
            source: "built-in" | "global" | "project" | "session";
            enabled: boolean;
            path?: string | undefined;
        }[];
        defaultPrompt?: string | undefined;
        templateEngine?: "mustache" | "handlebars" | undefined;
        cacheEnabled?: boolean | undefined;
        cacheTtl?: number | undefined;
    }, {
        loaders: {
            priority: number;
            source: "built-in" | "global" | "project" | "session";
            enabled: boolean;
            path?: string | undefined;
        }[];
        defaultPrompt?: string | undefined;
        templateEngine?: "mustache" | "handlebars" | undefined;
        cacheEnabled?: boolean | undefined;
        cacheTtl?: number | undefined;
    }>;
    ui: z.ZodObject<{
        theme: z.ZodEnum<["light", "dark", "auto"]>;
        animations: z.ZodBoolean;
        verbose: z.ZodBoolean;
        showTimestamps: z.ZodOptional<z.ZodBoolean>;
        showTokenCount: z.ZodOptional<z.ZodBoolean>;
        autoScroll: z.ZodOptional<z.ZodBoolean>;
        maxHistoryLines: z.ZodOptional<z.ZodNumber>;
        colors: z.ZodOptional<z.ZodObject<{
            primary: z.ZodString;
            secondary: z.ZodString;
            success: z.ZodString;
            warning: z.ZodString;
            error: z.ZodString;
            info: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            primary: string;
            secondary: string;
            success: string;
            warning: string;
            error: string;
            info: string;
        }, {
            primary: string;
            secondary: string;
            success: string;
            warning: string;
            error: string;
            info: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        theme: "light" | "dark" | "auto";
        animations: boolean;
        verbose: boolean;
        showTimestamps?: boolean | undefined;
        showTokenCount?: boolean | undefined;
        autoScroll?: boolean | undefined;
        maxHistoryLines?: number | undefined;
        colors?: {
            primary: string;
            secondary: string;
            success: string;
            warning: string;
            error: string;
            info: string;
        } | undefined;
    }, {
        theme: "light" | "dark" | "auto";
        animations: boolean;
        verbose: boolean;
        showTimestamps?: boolean | undefined;
        showTokenCount?: boolean | undefined;
        autoScroll?: boolean | undefined;
        maxHistoryLines?: number | undefined;
        colors?: {
            primary: string;
            secondary: string;
            success: string;
            warning: string;
            error: string;
            info: string;
        } | undefined;
    }>;
    logging: z.ZodObject<{
        level: z.ZodEnum<["debug", "info", "warn", "error"]>;
        format: z.ZodEnum<["text", "json"]>;
        output: z.ZodEnum<["console", "file", "both"]>;
        file: z.ZodOptional<z.ZodObject<{
            path: z.ZodString;
            maxSize: z.ZodString;
            maxFiles: z.ZodNumber;
            compress: z.ZodBoolean;
        }, "strip", z.ZodTypeAny, {
            path: string;
            maxSize: string;
            maxFiles: number;
            compress: boolean;
        }, {
            path: string;
            maxSize: string;
            maxFiles: number;
            compress: boolean;
        }>>;
        structured: z.ZodBoolean;
        includeTimestamp: z.ZodBoolean;
        includeLevel: z.ZodBoolean;
        includeSource: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        level: "error" | "info" | "debug" | "warn";
        format: "text" | "json";
        output: "console" | "file" | "both";
        structured: boolean;
        includeTimestamp: boolean;
        includeLevel: boolean;
        includeSource: boolean;
        file?: {
            path: string;
            maxSize: string;
            maxFiles: number;
            compress: boolean;
        } | undefined;
    }, {
        level: "error" | "info" | "debug" | "warn";
        format: "text" | "json";
        output: "console" | "file" | "both";
        structured: boolean;
        includeTimestamp: boolean;
        includeLevel: boolean;
        includeSource: boolean;
        file?: {
            path: string;
            maxSize: string;
            maxFiles: number;
            compress: boolean;
        } | undefined;
    }>;
    telemetry: z.ZodObject<{
        enabled: z.ZodBoolean;
        endpoint: z.ZodOptional<z.ZodString>;
        apiKey: z.ZodOptional<z.ZodString>;
        collectUsage: z.ZodBoolean;
        collectErrors: z.ZodBoolean;
        collectPerformance: z.ZodBoolean;
        anonymize: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        enabled: boolean;
        collectUsage: boolean;
        collectErrors: boolean;
        collectPerformance: boolean;
        anonymize: boolean;
        apiKey?: string | undefined;
        endpoint?: string | undefined;
    }, {
        enabled: boolean;
        collectUsage: boolean;
        collectErrors: boolean;
        collectPerformance: boolean;
        anonymize: boolean;
        apiKey?: string | undefined;
        endpoint?: string | undefined;
    }>;
    security: z.ZodObject<{
        sandbox: z.ZodObject<{
            enabled: z.ZodBoolean;
            type: z.ZodEnum<["docker", "podman", "native"]>;
            image: z.ZodOptional<z.ZodString>;
            allowNetworking: z.ZodBoolean;
            allowFileSystem: z.ZodBoolean;
            allowedPaths: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            blockedPaths: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        }, "strip", z.ZodTypeAny, {
            type: "docker" | "podman" | "native";
            enabled: boolean;
            allowNetworking: boolean;
            allowFileSystem: boolean;
            image?: string | undefined;
            allowedPaths?: string[] | undefined;
            blockedPaths?: string[] | undefined;
        }, {
            type: "docker" | "podman" | "native";
            enabled: boolean;
            allowNetworking: boolean;
            allowFileSystem: boolean;
            image?: string | undefined;
            allowedPaths?: string[] | undefined;
            blockedPaths?: string[] | undefined;
        }>;
        encryption: z.ZodObject<{
            enabled: z.ZodBoolean;
            algorithm: z.ZodString;
            keyDerivation: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            algorithm: string;
            keyDerivation: string;
        }, {
            enabled: boolean;
            algorithm: string;
            keyDerivation: string;
        }>;
        authentication: z.ZodObject<{
            required: z.ZodBoolean;
            methods: z.ZodArray<z.ZodString, "many">;
            tokenExpiry: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            required: boolean;
            methods: string[];
            tokenExpiry: number;
        }, {
            required: boolean;
            methods: string[];
            tokenExpiry: number;
        }>;
    }, "strip", z.ZodTypeAny, {
        sandbox: {
            type: "docker" | "podman" | "native";
            enabled: boolean;
            allowNetworking: boolean;
            allowFileSystem: boolean;
            image?: string | undefined;
            allowedPaths?: string[] | undefined;
            blockedPaths?: string[] | undefined;
        };
        encryption: {
            enabled: boolean;
            algorithm: string;
            keyDerivation: string;
        };
        authentication: {
            required: boolean;
            methods: string[];
            tokenExpiry: number;
        };
    }, {
        sandbox: {
            type: "docker" | "podman" | "native";
            enabled: boolean;
            allowNetworking: boolean;
            allowFileSystem: boolean;
            image?: string | undefined;
            allowedPaths?: string[] | undefined;
            blockedPaths?: string[] | undefined;
        };
        encryption: {
            enabled: boolean;
            algorithm: string;
            keyDerivation: string;
        };
        authentication: {
            required: boolean;
            methods: string[];
            tokenExpiry: number;
        };
    }>;
    performance: z.ZodObject<{
        maxConcurrentRequests: z.ZodNumber;
        requestTimeout: z.ZodNumber;
        retryAttempts: z.ZodNumber;
        retryDelay: z.ZodNumber;
        caching: z.ZodObject<{
            enabled: z.ZodBoolean;
            ttl: z.ZodNumber;
            maxSize: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            maxSize: number;
            ttl: number;
        }, {
            enabled: boolean;
            maxSize: number;
            ttl: number;
        }>;
        rateLimit: z.ZodObject<{
            enabled: z.ZodBoolean;
            requestsPerMinute: z.ZodNumber;
            burstLimit: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            requestsPerMinute: number;
            burstLimit: number;
        }, {
            enabled: boolean;
            requestsPerMinute: number;
            burstLimit: number;
        }>;
    }, "strip", z.ZodTypeAny, {
        maxConcurrentRequests: number;
        requestTimeout: number;
        retryAttempts: number;
        retryDelay: number;
        caching: {
            enabled: boolean;
            maxSize: number;
            ttl: number;
        };
        rateLimit: {
            enabled: boolean;
            requestsPerMinute: number;
            burstLimit: number;
        };
    }, {
        maxConcurrentRequests: number;
        requestTimeout: number;
        retryAttempts: number;
        retryDelay: number;
        caching: {
            enabled: boolean;
            maxSize: number;
            ttl: number;
        };
        rateLimit: {
            enabled: boolean;
            requestsPerMinute: number;
            burstLimit: number;
        };
    }>;
    custom: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
}, "strip", z.ZodTypeAny, {
    version: string;
    security: {
        sandbox: {
            type: "docker" | "podman" | "native";
            enabled: boolean;
            allowNetworking: boolean;
            allowFileSystem: boolean;
            image?: string | undefined;
            allowedPaths?: string[] | undefined;
            blockedPaths?: string[] | undefined;
        };
        encryption: {
            enabled: boolean;
            algorithm: string;
            keyDerivation: string;
        };
        authentication: {
            required: boolean;
            methods: string[];
            tokenExpiry: number;
        };
    };
    environment: "development" | "production" | "test";
    defaultProvider: string;
    providers: Record<string, {
        type: "custom" | "openai" | "anthropic" | "google" | "mistral" | "openrouter" | "local";
        enabled?: boolean | undefined;
        apiKey?: string | undefined;
        baseUrl?: string | undefined;
        timeout?: number | undefined;
        retries?: number | undefined;
        headers?: Record<string, string> | undefined;
        proxy?: string | undefined;
    }>;
    tools: {
        enabled: string[];
        file?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        git?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        web?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        shell?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        memory?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
    };
    prompts: {
        loaders: {
            priority: number;
            source: "built-in" | "global" | "project" | "session";
            enabled: boolean;
            path?: string | undefined;
        }[];
        defaultPrompt?: string | undefined;
        templateEngine?: "mustache" | "handlebars" | undefined;
        cacheEnabled?: boolean | undefined;
        cacheTtl?: number | undefined;
    };
    ui: {
        theme: "light" | "dark" | "auto";
        animations: boolean;
        verbose: boolean;
        showTimestamps?: boolean | undefined;
        showTokenCount?: boolean | undefined;
        autoScroll?: boolean | undefined;
        maxHistoryLines?: number | undefined;
        colors?: {
            primary: string;
            secondary: string;
            success: string;
            warning: string;
            error: string;
            info: string;
        } | undefined;
    };
    logging: {
        level: "error" | "info" | "debug" | "warn";
        format: "text" | "json";
        output: "console" | "file" | "both";
        structured: boolean;
        includeTimestamp: boolean;
        includeLevel: boolean;
        includeSource: boolean;
        file?: {
            path: string;
            maxSize: string;
            maxFiles: number;
            compress: boolean;
        } | undefined;
    };
    telemetry: {
        enabled: boolean;
        collectUsage: boolean;
        collectErrors: boolean;
        collectPerformance: boolean;
        anonymize: boolean;
        apiKey?: string | undefined;
        endpoint?: string | undefined;
    };
    performance: {
        maxConcurrentRequests: number;
        requestTimeout: number;
        retryAttempts: number;
        retryDelay: number;
        caching: {
            enabled: boolean;
            maxSize: number;
            ttl: number;
        };
        rateLimit: {
            enabled: boolean;
            requestsPerMinute: number;
            burstLimit: number;
        };
    };
    custom?: Record<string, unknown> | undefined;
    profile?: string | undefined;
}, {
    version: string;
    security: {
        sandbox: {
            type: "docker" | "podman" | "native";
            enabled: boolean;
            allowNetworking: boolean;
            allowFileSystem: boolean;
            image?: string | undefined;
            allowedPaths?: string[] | undefined;
            blockedPaths?: string[] | undefined;
        };
        encryption: {
            enabled: boolean;
            algorithm: string;
            keyDerivation: string;
        };
        authentication: {
            required: boolean;
            methods: string[];
            tokenExpiry: number;
        };
    };
    environment: "development" | "production" | "test";
    defaultProvider: string;
    providers: Record<string, {
        type: "custom" | "openai" | "anthropic" | "google" | "mistral" | "openrouter" | "local";
        enabled?: boolean | undefined;
        apiKey?: string | undefined;
        baseUrl?: string | undefined;
        timeout?: number | undefined;
        retries?: number | undefined;
        headers?: Record<string, string> | undefined;
        proxy?: string | undefined;
    }>;
    tools: {
        enabled: string[];
        file?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        git?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        web?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        shell?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
        memory?: {
            enabled: boolean;
            options?: Record<string, unknown> | undefined;
            timeout?: number | undefined;
            retries?: number | undefined;
        } | undefined;
    };
    prompts: {
        loaders: {
            priority: number;
            source: "built-in" | "global" | "project" | "session";
            enabled: boolean;
            path?: string | undefined;
        }[];
        defaultPrompt?: string | undefined;
        templateEngine?: "mustache" | "handlebars" | undefined;
        cacheEnabled?: boolean | undefined;
        cacheTtl?: number | undefined;
    };
    ui: {
        theme: "light" | "dark" | "auto";
        animations: boolean;
        verbose: boolean;
        showTimestamps?: boolean | undefined;
        showTokenCount?: boolean | undefined;
        autoScroll?: boolean | undefined;
        maxHistoryLines?: number | undefined;
        colors?: {
            primary: string;
            secondary: string;
            success: string;
            warning: string;
            error: string;
            info: string;
        } | undefined;
    };
    logging: {
        level: "error" | "info" | "debug" | "warn";
        format: "text" | "json";
        output: "console" | "file" | "both";
        structured: boolean;
        includeTimestamp: boolean;
        includeLevel: boolean;
        includeSource: boolean;
        file?: {
            path: string;
            maxSize: string;
            maxFiles: number;
            compress: boolean;
        } | undefined;
    };
    telemetry: {
        enabled: boolean;
        collectUsage: boolean;
        collectErrors: boolean;
        collectPerformance: boolean;
        anonymize: boolean;
        apiKey?: string | undefined;
        endpoint?: string | undefined;
    };
    performance: {
        maxConcurrentRequests: number;
        requestTimeout: number;
        retryAttempts: number;
        retryDelay: number;
        caching: {
            enabled: boolean;
            maxSize: number;
            ttl: number;
        };
        rateLimit: {
            enabled: boolean;
            requestsPerMinute: number;
            burstLimit: number;
        };
    };
    custom?: Record<string, unknown> | undefined;
    profile?: string | undefined;
}>;
//# sourceMappingURL=config.d.ts.map