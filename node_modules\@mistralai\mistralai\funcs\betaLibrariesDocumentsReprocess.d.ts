import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import * as errors from "../models/errors/index.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Reprocess a document.
 *
 * @remarks
 * Given a library and a document in that library, reprocess that document, it will be billed again.
 */
export declare function betaLibrariesDocumentsReprocess(client: MistralCore, request: operations.LibrariesDocumentsReprocessV1Request, options?: RequestOptions): APIPromise<Result<void, errors.HTTPValidationError | SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=betaLibrariesDocumentsReprocess.d.ts.map