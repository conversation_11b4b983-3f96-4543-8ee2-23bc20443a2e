/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Default configuration values
 */

export const DEFAULT_CONFIG = {
  version: '2.0.0',
  environment: 'production' as const,
  defaultProvider: 'openai',
  providers: {
    default: {
      type: 'openai' as const,
      enabled: true,
      timeout: 30000,
      retries: 3,
    },
    openai: {
      type: 'openai' as const,
      enabled: true,
      timeout: 30000,
      retries: 3,
    },
    anthropic: {
      type: 'anthropic' as const,
      enabled: true,
      timeout: 30000,
      retries: 3,
    },
    google: {
      type: 'google' as const,
      enabled: true,
      timeout: 30000,
      retries: 3,
    },
  },
  tools: {
    enabled: ['file', 'git', 'web', 'shell'],
    file: {
      enabled: true,
      timeout: 10000,
      retries: 2,
    },
    git: {
      enabled: true,
      timeout: 15000,
      retries: 2,
    },
    web: {
      enabled: true,
      timeout: 30000,
      retries: 3,
    },
    shell: {
      enabled: true,
      timeout: 60000,
      retries: 1,
    },
  },
  prompts: {
    defaultPrompt: 'developer-general',
    loaders: [
      {
        source: 'built-in' as const,
        enabled: true,
        priority: 10,
      },
      {
        source: 'global' as const,
        enabled: true,
        priority: 50,
      },
      {
        source: 'project' as const,
        enabled: true,
        priority: 75,
      },
      {
        source: 'session' as const,
        enabled: true,
        priority: 100,
      },
    ],
    templateEngine: 'mustache' as const,
    cacheEnabled: true,
    cacheTtl: 300000, // 5 minutes
  },
  ui: {
    theme: 'dark' as const,
    animations: true,
    verbose: false,
    showTimestamps: true,
    showTokenCount: true,
    autoScroll: true,
    maxHistoryLines: 1000,
    colors: {
      primary: '#00d4ff',
      secondary: '#ff6b6b',
      success: '#51cf66',
      warning: '#ffd43b',
      error: '#ff6b6b',
      info: '#74c0fc',
    },
  },
  logging: {
    level: 'info' as const,
    format: 'text' as const,
    output: 'console' as const,
    structured: false,
    includeTimestamp: true,
    includeLevel: true,
    includeSource: false,
  },
  telemetry: {
    enabled: false,
    collectUsage: false,
    collectErrors: false,
    collectPerformance: false,
    anonymize: true,
  },
  security: {
    sandbox: {
      enabled: false,
      type: 'docker' as const,
      allowNetworking: true,
      allowFileSystem: true,
    },
    encryption: {
      enabled: false,
      algorithm: 'aes-256-gcm',
      keyDerivation: 'pbkdf2',
    },
    authentication: {
      required: false,
      methods: ['api-key'],
      tokenExpiry: 3600,
    },
  },
  performance: {
    maxConcurrentRequests: 10,
    requestTimeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
    caching: {
      enabled: true,
      ttl: 300,
      maxSize: 100,
    },
    rateLimit: {
      enabled: false,
      requestsPerMinute: 60,
      burstLimit: 10,
    },
  },
} as const;
