/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { PromptTemplate, PromptContext, RenderedPrompt, PromptValidationResult, Prompt<PERSON><PERSON><PERSON> as IPromptRenderer } from '@inkbytefo/s647-shared';
/**
 * Simple Mustache-style template renderer
 * Supports basic variable interpolation and conditional blocks
 */
export declare class PromptRenderer implements IPromptRenderer {
    /**
     * Render a prompt template with context
     */
    render(template: PromptTemplate, context: PromptContext): Promise<RenderedPrompt>;
    /**
     * Validate a prompt template
     */
    validate(template: PromptTemplate): PromptValidationResult;
    /**
     * Extract variables from template content
     */
    extractVariables(content: string): string[];
    /**
     * Process conditional blocks
     */
    private processConditionals;
    /**
     * Process variable interpolation
     */
    private processVariables;
    /**
     * Evaluate a conditional expression
     */
    private evaluateCondition;
    /**
     * Get variable value from context
     */
    private getVariableValue;
    /**
     * Extract variable name from condition
     */
    private extractVariableFromCondition;
    /**
     * Check if a variable is built-in
     */
    private isBuiltInVariable;
}
//# sourceMappingURL=mustache.d.ts.map