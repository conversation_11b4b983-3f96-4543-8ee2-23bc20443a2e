/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  PromptTemplate,
  PromptContext,
  RenderedPrompt,
  PromptValidationResult,
  PromptRenderer as IPromptRenderer,
} from '@inkbytefo/s647-shared';

/**
 * Simple Mustache-style template renderer
 * Supports basic variable interpolation and conditional blocks
 */
export class PromptRenderer implements IPromptRenderer {
  /**
   * Render a prompt template with context
   */
  async render(template: PromptTemplate, context: PromptContext): Promise<RenderedPrompt> {
    let content = template.content;
    const variables: Record<string, any> = {};

    // Add built-in variables
    const enrichedContext: PromptContext = {
      ...context,
      date: new Date().toISOString().split('T')[0] || '',
      time: new Date().toTimeString().split(' ')[0] || '',
      timestamp: Date.now(),
      random: Math.random().toString(36).substring(7),
    };

    // Process conditional blocks first
    content = this.processConditionals(content, enrichedContext);

    // Process variable interpolation
    content = this.processVariables(content, enrichedContext, variables);

    return {
      id: template.id,
      content: content.trim(),
      metadata: template.metadata,
      context: enrichedContext,
      variables,
    };
  }

  /**
   * Validate a prompt template
   */
  validate(template: PromptTemplate): PromptValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check for balanced conditional blocks
      const conditionalRegex = /\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
      let match;
      
      while ((match = conditionalRegex.exec(template.content)) !== null) {
        const condition = match[1]?.trim();
        if (!condition) {
          errors.push('Empty conditional block found');
        }
      }

      // Check for unclosed blocks
      const openBlocks = (template.content.match(/\{\{#if/g) || []).length;
      const closeBlocks = (template.content.match(/\{\{\/if\}\}/g) || []).length;
      
      if (openBlocks !== closeBlocks) {
        errors.push('Unbalanced conditional blocks');
      }

      // Check for invalid variable syntax
      const invalidVars = template.content.match(/\{\{[^}]*\{\{|\}\}[^{]*\}\}/g);
      if (invalidVars) {
        errors.push('Invalid variable syntax found');
      }

    } catch (error) {
      errors.push(`Template validation error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Extract variables from template content
   */
  extractVariables(content: string): string[] {
    const variables = new Set<string>();
    
    // Extract simple variables {{variable}}
    const simpleVarRegex = /\{\{([^#/][^}]*)\}\}/g;
    let match;
    
    while ((match = simpleVarRegex.exec(content)) !== null) {
      const variable = match[1]?.trim();
      if (variable && !this.isBuiltInVariable(variable)) {
        variables.add(variable);
      }
    }

    // Extract conditional variables {{#if variable}}
    const conditionalRegex = /\{\{#if\s+([^}]+)\}\}/g;
    while ((match = conditionalRegex.exec(content)) !== null) {
      const condition = match[1]?.trim();
      if (condition) {
        const variable = this.extractVariableFromCondition(condition);
        if (variable && !this.isBuiltInVariable(variable)) {
          variables.add(variable);
        }
      }
    }

    return Array.from(variables);
  }

  /**
   * Process conditional blocks
   */
  private processConditionals(content: string, context: PromptContext): string {
    const conditionalRegex = /\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
    
    return content.replace(conditionalRegex, (_match, condition, block) => {
      const shouldInclude = this.evaluateCondition(condition.trim(), context);
      return shouldInclude ? block : '';
    });
  }

  /**
   * Process variable interpolation
   */
  private processVariables(
    content: string, 
    context: PromptContext, 
    variables: Record<string, any>
  ): string {
    const variableRegex = /\{\{([^#/][^}]*)\}\}/g;
    
    return content.replace(variableRegex, (match, variable) => {
      const varName = variable.trim();
      const value = this.getVariableValue(varName, context);
      
      variables[varName] = value;
      
      return value !== undefined && value !== null ? String(value) : match;
    });
  }

  /**
   * Evaluate a conditional expression
   */
  private evaluateCondition(condition: string, context: PromptContext): boolean {
    // Simple condition evaluation
    // Supports: variable, !variable, variable === 'value', variable !== 'value'
    
    if (condition.startsWith('!')) {
      const variable = condition.substring(1).trim();
      const value = this.getVariableValue(variable, context);
      return !value;
    }

    if (condition.includes('===')) {
      const parts = condition.split('===').map(s => s.trim());
      const variable = parts[0];
      const expectedValue = parts[1];
      if (variable && expectedValue) {
        const value = this.getVariableValue(variable, context);
        const expected = expectedValue.replace(/['"]/g, '');
        return String(value) === expected;
      }
    }

    if (condition.includes('!==')) {
      const parts = condition.split('!==').map(s => s.trim());
      const variable = parts[0];
      const expectedValue = parts[1];
      if (variable && expectedValue) {
        const value = this.getVariableValue(variable, context);
        const expected = expectedValue.replace(/['"]/g, '');
        return String(value) !== expected;
      }
    }

    // Simple truthiness check
    const value = this.getVariableValue(condition, context);
    return Boolean(value);
  }

  /**
   * Get variable value from context
   */
  private getVariableValue(variable: string, context: PromptContext): any {
    // Support dot notation for nested properties
    const parts = variable.split('.');
    let value: any = context;
    
    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  }

  /**
   * Extract variable name from condition
   */
  private extractVariableFromCondition(condition: string): string | undefined {
    if (condition.startsWith('!')) {
      return condition.substring(1).trim();
    }

    if (condition.includes('===') || condition.includes('!==')) {
      const parts = condition.split(/===|!==/);
      return parts[0]?.trim();
    }

    return condition.trim();
  }

  /**
   * Check if a variable is built-in
   */
  private isBuiltInVariable(variable: string): boolean {
    const builtInVars = ['date', 'time', 'timestamp', 'random'];
    return builtInVars.includes(variable);
  }
}
