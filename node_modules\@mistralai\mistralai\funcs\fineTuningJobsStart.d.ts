import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Start Fine Tuning Job
 *
 * @remarks
 * Request the start of a validated fine tuning job.
 */
export declare function fineTuningJobsStart(client: MistralCore, request: operations.JobsApiRoutesFineTuningStartFineTuningJobRequest, options?: RequestOptions): APIPromise<Result<operations.JobsApiRoutesFineTuningStartFineTuningJobResponse, SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=fineTuningJobsStart.d.ts.map