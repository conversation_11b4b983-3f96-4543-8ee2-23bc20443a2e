/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { ProviderConfig } from './providers.js';
import type { Environment, ThemeMode, LogLevel } from './common.js';
import type { PromptManagerConfig } from './prompts.js';

/**
 * Configuration types
 */

export interface Configuration {
  version: string;
  environment: Environment;
  profile?: string;
  defaultProvider: string;
  providers: Record<string, ProviderConfig>;
  tools: {
    enabled: string[];
    [key: string]: any;
  };
  prompts: PromptManagerConfig;
  ui: {
    theme: ThemeMode;
    animations: boolean;
    verbose: boolean;
    [key: string]: any;
  };
  logging: {
    level: LogLevel;
    format: 'text' | 'json';
    output: 'console' | 'file' | 'both';
    [key: string]: any;
  };
  telemetry: {
    enabled: boolean;
    [key: string]: any;
  };
  security: {
    [key: string]: any;
  };
  performance: {
    [key: string]: any;
  };
  custom?: Record<string, unknown>;
}
