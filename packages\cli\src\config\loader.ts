/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { Configuration, AsyncResult } from '@inkbytefo/s647-shared';
import { ConfigurationManager } from '@inkbytefo/s647-core-refactored';
import { loadEnvConfig, getProviderConfig, getAvailableProviders, validateProviders, type EnvConfig } from '@inkbytefo/s647-shared';
import type { CliArgs } from './args.js';

/**
 * Load configuration using environment variables and configuration manager
 */
export async function loadConfiguration(args: CliArgs): Promise<Configuration> {
  // Load environment configuration first
  const envConfig = loadEnvConfig();

  // Validate providers
  validateProviders(envConfig);

  // Get available providers
  const availableProviders = getAvailableProviders(envConfig);

  // Create configuration from environment
  const configuration = createConfigurationFromEnv(envConfig, availableProviders);

  // Apply CLI args overrides
  applyCliArgsOverrides(configuration, args);

  return configuration;
}

/**
 * Create configuration object from environment variables
 */
function createConfigurationFromEnv(env: EnvConfig, availableProviders: any[]): Configuration {
  // Build providers configuration
  const providers: Record<string, any> = {};

  for (const provider of availableProviders) {
    providers[provider.type] = {
      type: provider.type,
      enabled: provider.enabled,
      apiKey: provider.apiKey,
      baseUrl: provider.baseUrl,
      model: provider.model,
      maxTokens: provider.maxTokens,
      temperature: provider.temperature,
      timeout: env.REQUEST_TIMEOUT,
      retries: env.RETRY_ATTEMPTS,
    };
  }

  return {
    version: '2.0.0',
    environment: env.NODE_ENV as any,
    defaultProvider: env.DEFAULT_PROVIDER,
    providers,
    tools: {
      enabled: env.MCP_ENABLED ? ['mcp', 'file', 'memory'] : ['file'],
      mcp: {
        enabled: env.MCP_ENABLED,
        configPath: env.MCP_SERVERS_CONFIG_PATH,
      },
      file: {
        enabled: env.FILE_INTEGRATION_ENABLED,
        maxSize: env.FILE_MAX_SIZE,
        allowedExtensions: env.FILE_ALLOWED_EXTENSIONS.split(','),
      },
      memory: {
        enabled: env.MEMORY_ENABLED,
        maxSize: env.MEMORY_MAX_SIZE,
        ttl: env.MEMORY_TTL,
      },
    },
    prompts: {
      defaultPrompt: 'developer-general',
      loaders: [
        {
          source: 'built-in' as const,
          enabled: true,
          priority: 10,
        },
        {
          source: 'global' as const,
          enabled: true,
          priority: 50,
        },
        {
          source: 'project' as const,
          enabled: true,
          priority: 75,
        },
        {
          source: 'session' as const,
          enabled: true,
          priority: 100,
        },
      ],
      templateEngine: 'mustache' as const,
      cacheEnabled: true,
      cacheTtl: 300000,
    },
    ui: {
      theme: env.UI_THEME,
      animations: env.UI_ANIMATIONS,
      showTimestamps: env.UI_SHOW_TIMESTAMPS,
      showTokenCount: env.UI_SHOW_TOKEN_COUNT,
      maxHistoryLines: env.UI_MAX_HISTORY_LINES,
      verbose: env.DEBUG,
      autoScroll: true,
      colors: {
        primary: '#00d4ff',
        secondary: '#ff6b6b',
        success: '#51cf66',
        warning: '#ffd43b',
        error: '#ff6b6b',
        info: '#74c0fc',
      },
    },
    logging: {
      level: env.LOG_LEVEL,
      format: env.LOG_FORMAT,
      output: 'console',
      structured: env.LOG_FORMAT === 'json',
      includeTimestamp: true,
      includeLevel: true,
      includeSource: env.DEBUG,
    },
    telemetry: {
      enabled: env.TELEMETRY_ENABLED,
      collectUsage: env.TELEMETRY_ENABLED,
      collectErrors: env.TELEMETRY_ENABLED,
      collectPerformance: env.TELEMETRY_ENABLED,
      anonymize: true,
    },
    security: {
      sandbox: {
        enabled: env.SANDBOX_ENABLED,
        type: 'docker',
        allowNetworking: true,
        allowFileSystem: true,
      },
      encryption: {
        enabled: env.ENCRYPTION_ENABLED,
        algorithm: 'aes-256-gcm',
        keyDerivation: 'pbkdf2',
      },
      authentication: {
        required: false,
        methods: ['api-key'],
        tokenExpiry: 3600,
      },
    },
    performance: {
      maxConcurrentRequests: env.MAX_CONCURRENT_REQUESTS,
      requestTimeout: env.REQUEST_TIMEOUT,
      retryAttempts: env.RETRY_ATTEMPTS,
      retryDelay: env.RETRY_DELAY,
      caching: {
        enabled: true,
        ttl: 300,
        maxSize: 100,
      },
      rateLimit: {
        enabled: false,
        requestsPerMinute: 60,
        burstLimit: 10,
      },
    },
  };
}

/**
 * Apply CLI arguments overrides to configuration
 */
function applyCliArgsOverrides(config: Configuration, args: CliArgs): void {
  // Override logging level if debug flag is set
  if (args.debug) {
    config.logging.level = 'debug';
    config.ui.verbose = true;
  }

  // Override quiet mode
  if (args.quiet) {
    config.logging.level = 'error';
    config.ui.verbose = false;
  }

  // Override provider if specified
  if (args.provider && config.providers[args.provider]) {
    config.defaultProvider = args.provider;
  }
}
