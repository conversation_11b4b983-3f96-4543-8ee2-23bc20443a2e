/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { FilePromptLoader } from './file.js';
/**
 * Prompt loader registry
 * Manages multiple prompt loaders and coordinates loading from different sources
 */
export class PromptLoaderRegistry {
    loaders = new Map();
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Initialize the registry with loader configurations
     */
    async initialize(configs) {
        this.logger?.info('Initializing prompt loader registry', { configs });
        for (const config of configs) {
            if (!config.enabled) {
                this.logger?.debug(`Skipping disabled loader: ${config.source}`);
                continue;
            }
            try {
                const loader = this.createLoader(config);
                this.loaders.set(config.source, loader);
                this.logger?.info(`Initialized loader: ${config.source}`, { priority: config.priority });
            }
            catch (error) {
                this.logger?.error(`Failed to initialize loader: ${config.source}`, {
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
    }
    /**
     * Load prompts from all registered loaders
     */
    async loadAllPrompts() {
        const allPrompts = [];
        const loaderEntries = Array.from(this.loaders.entries());
        // Sort by priority (higher priority first)
        loaderEntries.sort((a, b) => b[1].priority - a[1].priority);
        for (const [source, loader] of loaderEntries) {
            try {
                this.logger?.debug(`Loading prompts from: ${source}`);
                const prompts = await loader.load();
                allPrompts.push(...prompts);
                this.logger?.info(`Loaded prompts from ${source}`, { count: prompts.length });
            }
            catch (error) {
                this.logger?.error(`Failed to load prompts from ${source}`, {
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        // Deduplicate by ID (higher priority loaders win)
        const uniquePrompts = new Map();
        for (const prompt of allPrompts) {
            if (!uniquePrompts.has(prompt.id)) {
                uniquePrompts.set(prompt.id, prompt);
            }
        }
        return Array.from(uniquePrompts.values());
    }
    /**
     * Get a specific loader
     */
    getLoader(source) {
        return this.loaders.get(source);
    }
    /**
     * Check if a prompt exists in any loader
     */
    async exists(id) {
        for (const loader of this.loaders.values()) {
            try {
                if (await loader.exists(id)) {
                    return true;
                }
            }
            catch (error) {
                this.logger?.warn(`Error checking existence in loader`, {
                    source: loader.source,
                    id,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        return false;
    }
    /**
     * Save prompts to appropriate loaders
     */
    async savePrompts(prompts, source = 'project') {
        const loader = this.loaders.get(source);
        if (!loader) {
            throw new Error(`No loader found for source: ${source}`);
        }
        try {
            await loader.save(prompts);
            this.logger?.info(`Saved prompts to ${source}`, { count: prompts.length });
        }
        catch (error) {
            this.logger?.error(`Failed to save prompts to ${source}`, {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Create a loader instance based on configuration
     */
    createLoader(config) {
        switch (config.source) {
            case 'global':
                return new FilePromptLoader('global', config.priority, config.path || this.getDefaultGlobalPath(), this.logger);
            case 'project':
                return new FilePromptLoader('project', config.priority, config.path || this.getDefaultProjectPath(), this.logger);
            case 'session':
                // Session loader could be in-memory or temporary file-based
                return new FilePromptLoader('session', config.priority, config.path || this.getDefaultSessionPath(), this.logger);
            case 'built-in':
                // Built-in prompts are handled by the BuiltInPromptLibrary
                // This is a placeholder for consistency
                throw new Error('Built-in loader should not be created through registry');
            default:
                throw new Error(`Unknown prompt source: ${config.source}`);
        }
    }
    /**
     * Get default global prompts path
     */
    getDefaultGlobalPath() {
        const os = process.platform;
        const homeDir = process.env.HOME || process.env.USERPROFILE || '';
        if (os === 'win32') {
            return `${homeDir}\\.s647\\prompts`;
        }
        else {
            return `${homeDir}/.s647/prompts`;
        }
    }
    /**
     * Get default project prompts path
     */
    getDefaultProjectPath() {
        return '.s647/prompts';
    }
    /**
     * Get default session prompts path
     */
    getDefaultSessionPath() {
        const os = process.platform;
        const tmpDir = process.env.TMPDIR || process.env.TEMP || '/tmp';
        if (os === 'win32') {
            return `${tmpDir}\\s647-session-prompts`;
        }
        else {
            return `${tmpDir}/s647-session-prompts`;
        }
    }
}
//# sourceMappingURL=registry.js.map