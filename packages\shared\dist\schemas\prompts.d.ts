/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
/**
 * Prompt schemas using Zod for runtime validation
 */
export declare const PromptVariableSchema: z.Zod<PERSON>n<[z.Zod<PERSON><PERSON>, z.<PERSON>, z.Z<PERSON>, z.ZodUnknown]>;
export declare const PromptContextSchema: z.ZodObject<{
    projectName: z.ZodOptional<z.ZodString>;
    projectType: z.ZodOptional<z.ZodString>;
    language: z.ZodOptional<z.ZodString>;
    framework: z.ZodOptional<z.ZodString>;
    fileName: z.ZodOptional<z.ZodString>;
    fileExtension: z.ZodOptional<z.ZodString>;
    filePath: z.ZodOptional<z.ZodString>;
    userName: z.ZodOptional<z.ZodString>;
    userRole: z.ZodOptional<z.ZodString>;
    currentTask: z.ZodOptional<z.ZodString>;
    sessionId: z.<PERSON>od<PERSON>ptional<z.ZodString>;
    timestamp: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, z.objectOutputType<{
    projectName: z.ZodOptional<z.ZodString>;
    projectType: z.ZodOptional<z.ZodString>;
    language: z.ZodOptional<z.ZodString>;
    framework: z.ZodOptional<z.ZodString>;
    fileName: z.ZodOptional<z.ZodString>;
    fileExtension: z.ZodOptional<z.ZodString>;
    filePath: z.ZodOptional<z.ZodString>;
    userName: z.ZodOptional<z.ZodString>;
    userRole: z.ZodOptional<z.ZodString>;
    currentTask: z.ZodOptional<z.ZodString>;
    sessionId: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodOptional<z.ZodNumber>;
}, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip">, z.objectInputType<{
    projectName: z.ZodOptional<z.ZodString>;
    projectType: z.ZodOptional<z.ZodString>;
    language: z.ZodOptional<z.ZodString>;
    framework: z.ZodOptional<z.ZodString>;
    fileName: z.ZodOptional<z.ZodString>;
    fileExtension: z.ZodOptional<z.ZodString>;
    filePath: z.ZodOptional<z.ZodString>;
    userName: z.ZodOptional<z.ZodString>;
    userRole: z.ZodOptional<z.ZodString>;
    currentTask: z.ZodOptional<z.ZodString>;
    sessionId: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodOptional<z.ZodNumber>;
}, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip">>;
export declare const PromptCategorySchema: z.ZodEnum<["role-based", "task-specific", "domain-specific", "communication-style", "custom"]>;
export declare const PromptMetadataSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodString;
    version: z.ZodString;
    author: z.ZodOptional<z.ZodString>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    category: z.ZodOptional<z.ZodEnum<["role-based", "task-specific", "domain-specific", "communication-style", "custom"]>>;
    language: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodOptional<z.ZodNumber>;
    updatedAt: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    name: string;
    description: string;
    version: string;
    language?: string | undefined;
    author?: string | undefined;
    tags?: string[] | undefined;
    category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
    createdAt?: number | undefined;
    updatedAt?: number | undefined;
}, {
    name: string;
    description: string;
    version: string;
    language?: string | undefined;
    author?: string | undefined;
    tags?: string[] | undefined;
    category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
    createdAt?: number | undefined;
    updatedAt?: number | undefined;
}>;
export declare const PromptConditionSchema: z.ZodObject<{
    variable: z.ZodString;
    operator: z.ZodEnum<["equals", "not-equals", "contains", "not-contains", "exists", "not-exists"]>;
    value: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>>;
}, "strip", z.ZodTypeAny, {
    variable: string;
    operator: "equals" | "not-equals" | "contains" | "not-contains" | "exists" | "not-exists";
    value?: unknown;
}, {
    variable: string;
    operator: "equals" | "not-equals" | "contains" | "not-contains" | "exists" | "not-exists";
    value?: unknown;
}>;
export declare const PromptTemplateSchema: z.ZodObject<{
    id: z.ZodString;
    metadata: z.ZodObject<{
        name: z.ZodString;
        description: z.ZodString;
        version: z.ZodString;
        author: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        category: z.ZodOptional<z.ZodEnum<["role-based", "task-specific", "domain-specific", "communication-style", "custom"]>>;
        language: z.ZodOptional<z.ZodString>;
        createdAt: z.ZodOptional<z.ZodNumber>;
        updatedAt: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    }, {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    }>;
    content: z.ZodString;
    variables: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    conditions: z.ZodOptional<z.ZodArray<z.ZodObject<{
        variable: z.ZodString;
        operator: z.ZodEnum<["equals", "not-equals", "contains", "not-contains", "exists", "not-exists"]>;
        value: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>>;
    }, "strip", z.ZodTypeAny, {
        variable: string;
        operator: "equals" | "not-equals" | "contains" | "not-contains" | "exists" | "not-exists";
        value?: unknown;
    }, {
        variable: string;
        operator: "equals" | "not-equals" | "contains" | "not-contains" | "exists" | "not-exists";
        value?: unknown;
    }>, "many">>;
    extends: z.ZodOptional<z.ZodString>;
    priority: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    id: string;
    metadata: {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    };
    content: string;
    variables?: string[] | undefined;
    conditions?: {
        variable: string;
        operator: "equals" | "not-equals" | "contains" | "not-contains" | "exists" | "not-exists";
        value?: unknown;
    }[] | undefined;
    extends?: string | undefined;
    priority?: number | undefined;
}, {
    id: string;
    metadata: {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    };
    content: string;
    variables?: string[] | undefined;
    conditions?: {
        variable: string;
        operator: "equals" | "not-equals" | "contains" | "not-contains" | "exists" | "not-exists";
        value?: unknown;
    }[] | undefined;
    extends?: string | undefined;
    priority?: number | undefined;
}>;
export declare const RenderedPromptSchema: z.ZodObject<{
    id: z.ZodString;
    content: z.ZodString;
    metadata: z.ZodObject<{
        name: z.ZodString;
        description: z.ZodString;
        version: z.ZodString;
        author: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        category: z.ZodOptional<z.ZodEnum<["role-based", "task-specific", "domain-specific", "communication-style", "custom"]>>;
        language: z.ZodOptional<z.ZodString>;
        createdAt: z.ZodOptional<z.ZodNumber>;
        updatedAt: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    }, {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    }>;
    context: z.ZodObject<{
        projectName: z.ZodOptional<z.ZodString>;
        projectType: z.ZodOptional<z.ZodString>;
        language: z.ZodOptional<z.ZodString>;
        framework: z.ZodOptional<z.ZodString>;
        fileName: z.ZodOptional<z.ZodString>;
        fileExtension: z.ZodOptional<z.ZodString>;
        filePath: z.ZodOptional<z.ZodString>;
        userName: z.ZodOptional<z.ZodString>;
        userRole: z.ZodOptional<z.ZodString>;
        currentTask: z.ZodOptional<z.ZodString>;
        sessionId: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, z.objectOutputType<{
        projectName: z.ZodOptional<z.ZodString>;
        projectType: z.ZodOptional<z.ZodString>;
        language: z.ZodOptional<z.ZodString>;
        framework: z.ZodOptional<z.ZodString>;
        fileName: z.ZodOptional<z.ZodString>;
        fileExtension: z.ZodOptional<z.ZodString>;
        filePath: z.ZodOptional<z.ZodString>;
        userName: z.ZodOptional<z.ZodString>;
        userRole: z.ZodOptional<z.ZodString>;
        currentTask: z.ZodOptional<z.ZodString>;
        sessionId: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodOptional<z.ZodNumber>;
    }, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip">, z.objectInputType<{
        projectName: z.ZodOptional<z.ZodString>;
        projectType: z.ZodOptional<z.ZodString>;
        language: z.ZodOptional<z.ZodString>;
        framework: z.ZodOptional<z.ZodString>;
        fileName: z.ZodOptional<z.ZodString>;
        fileExtension: z.ZodOptional<z.ZodString>;
        filePath: z.ZodOptional<z.ZodString>;
        userName: z.ZodOptional<z.ZodString>;
        userRole: z.ZodOptional<z.ZodString>;
        currentTask: z.ZodOptional<z.ZodString>;
        sessionId: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodOptional<z.ZodNumber>;
    }, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip">>;
    variables: z.ZodRecord<z.ZodString, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    metadata: {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    };
    content: string;
    variables: Record<string, unknown>;
    context: {
        projectName?: string | undefined;
        projectType?: string | undefined;
        language?: string | undefined;
        framework?: string | undefined;
        fileName?: string | undefined;
        fileExtension?: string | undefined;
        filePath?: string | undefined;
        userName?: string | undefined;
        userRole?: string | undefined;
        currentTask?: string | undefined;
        sessionId?: string | undefined;
        timestamp?: number | undefined;
    } & {
        [k: string]: unknown;
    };
}, {
    id: string;
    metadata: {
        name: string;
        description: string;
        version: string;
        language?: string | undefined;
        author?: string | undefined;
        tags?: string[] | undefined;
        category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
        createdAt?: number | undefined;
        updatedAt?: number | undefined;
    };
    content: string;
    variables: Record<string, unknown>;
    context: {
        projectName?: string | undefined;
        projectType?: string | undefined;
        language?: string | undefined;
        framework?: string | undefined;
        fileName?: string | undefined;
        fileExtension?: string | undefined;
        filePath?: string | undefined;
        userName?: string | undefined;
        userRole?: string | undefined;
        currentTask?: string | undefined;
        sessionId?: string | undefined;
        timestamp?: number | undefined;
    } & {
        [k: string]: unknown;
    };
}>;
export declare const PromptSourceSchema: z.ZodEnum<["built-in", "global", "project", "session"]>;
export declare const PromptLoaderConfigSchema: z.ZodObject<{
    source: z.ZodEnum<["built-in", "global", "project", "session"]>;
    path: z.ZodOptional<z.ZodString>;
    enabled: z.ZodBoolean;
    priority: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    priority: number;
    source: "built-in" | "global" | "project" | "session";
    enabled: boolean;
    path?: string | undefined;
}, {
    priority: number;
    source: "built-in" | "global" | "project" | "session";
    enabled: boolean;
    path?: string | undefined;
}>;
export declare const PromptManagerConfigSchema: z.ZodObject<{
    defaultPrompt: z.ZodOptional<z.ZodString>;
    loaders: z.ZodArray<z.ZodObject<{
        source: z.ZodEnum<["built-in", "global", "project", "session"]>;
        path: z.ZodOptional<z.ZodString>;
        enabled: z.ZodBoolean;
        priority: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        priority: number;
        source: "built-in" | "global" | "project" | "session";
        enabled: boolean;
        path?: string | undefined;
    }, {
        priority: number;
        source: "built-in" | "global" | "project" | "session";
        enabled: boolean;
        path?: string | undefined;
    }>, "many">;
    templateEngine: z.ZodOptional<z.ZodEnum<["mustache", "handlebars"]>>;
    cacheEnabled: z.ZodOptional<z.ZodBoolean>;
    cacheTtl: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    loaders: {
        priority: number;
        source: "built-in" | "global" | "project" | "session";
        enabled: boolean;
        path?: string | undefined;
    }[];
    defaultPrompt?: string | undefined;
    templateEngine?: "mustache" | "handlebars" | undefined;
    cacheEnabled?: boolean | undefined;
    cacheTtl?: number | undefined;
}, {
    loaders: {
        priority: number;
        source: "built-in" | "global" | "project" | "session";
        enabled: boolean;
        path?: string | undefined;
    }[];
    defaultPrompt?: string | undefined;
    templateEngine?: "mustache" | "handlebars" | undefined;
    cacheEnabled?: boolean | undefined;
    cacheTtl?: number | undefined;
}>;
export declare const PromptSelectorSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    category: z.ZodOptional<z.ZodEnum<["role-based", "task-specific", "domain-specific", "communication-style", "custom"]>>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    context: z.ZodOptional<z.ZodObject<{
        projectName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        projectType: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        language: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        framework: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileExtension: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        filePath: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userRole: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        currentTask: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        sessionId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        timestamp: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    }, "strip", z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, z.objectOutputType<{
        projectName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        projectType: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        language: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        framework: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileExtension: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        filePath: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userRole: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        currentTask: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        sessionId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        timestamp: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    }, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip">, z.objectInputType<{
        projectName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        projectType: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        language: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        framework: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileExtension: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        filePath: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userRole: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        currentTask: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        sessionId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        timestamp: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    }, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip">>>;
}, "strip", z.ZodTypeAny, {
    tags?: string[] | undefined;
    category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
    id?: string | undefined;
    context?: z.objectOutputType<{
        projectName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        projectType: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        language: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        framework: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileExtension: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        filePath: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userRole: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        currentTask: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        sessionId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        timestamp: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    }, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip"> | undefined;
}, {
    tags?: string[] | undefined;
    category?: "role-based" | "task-specific" | "domain-specific" | "communication-style" | "custom" | undefined;
    id?: string | undefined;
    context?: z.objectInputType<{
        projectName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        projectType: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        language: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        framework: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        fileExtension: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        filePath: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userName: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        userRole: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        currentTask: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        sessionId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
        timestamp: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    }, z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodUnknown]>, "strip"> | undefined;
}>;
export declare const PromptValidationResultSchema: z.ZodObject<{
    valid: z.ZodBoolean;
    errors: z.ZodArray<z.ZodString, "many">;
    warnings: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    valid: boolean;
    errors: string[];
    warnings: string[];
}, {
    valid: boolean;
    errors: string[];
    warnings: string[];
}>;
export declare const BuiltInPromptIdSchema: z.ZodEnum<["developer-general", "code-reviewer", "debugger", "architect", "mentor", "analyst", "documentation", "testing", "refactoring", "optimization", "security", "deployment", "concise", "detailed", "step-by-step", "creative", "formal", "casual", "web-development", "mobile-development", "data-science", "devops", "machine-learning"]>;
export type PromptVariableType = z.infer<typeof PromptVariableSchema>;
export type PromptContextType = z.infer<typeof PromptContextSchema>;
export type PromptCategoryType = z.infer<typeof PromptCategorySchema>;
export type PromptMetadataType = z.infer<typeof PromptMetadataSchema>;
export type PromptConditionType = z.infer<typeof PromptConditionSchema>;
export type PromptTemplateType = z.infer<typeof PromptTemplateSchema>;
export type RenderedPromptType = z.infer<typeof RenderedPromptSchema>;
export type PromptSourceType = z.infer<typeof PromptSourceSchema>;
export type PromptLoaderConfigType = z.infer<typeof PromptLoaderConfigSchema>;
export type PromptManagerConfigType = z.infer<typeof PromptManagerConfigSchema>;
export type PromptSelectorType = z.infer<typeof PromptSelectorSchema>;
export type PromptValidationResultType = z.infer<typeof PromptValidationResultSchema>;
export type BuiltInPromptIdType = z.infer<typeof BuiltInPromptIdSchema>;
//# sourceMappingURL=prompts.d.ts.map