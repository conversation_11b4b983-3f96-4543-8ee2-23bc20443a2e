"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.filesUpload = filesUpload;
const encodings_js_1 = require("../lib/encodings.js");
const files_js_1 = require("../lib/files.js");
const M = __importStar(require("../lib/matchers.js"));
const primitives_js_1 = require("../lib/primitives.js");
const schemas_js_1 = require("../lib/schemas.js");
const security_js_1 = require("../lib/security.js");
const url_js_1 = require("../lib/url.js");
const components = __importStar(require("../models/components/index.js"));
const operations = __importStar(require("../models/operations/index.js"));
const async_js_1 = require("../types/async.js");
const blobs_js_1 = require("../types/blobs.js");
const streams_js_1 = require("../types/streams.js");
/**
 * Upload File
 *
 * @remarks
 * Upload a file that can be used across various endpoints.
 *
 * The size of individual files can be a maximum of 512 MB. The Fine-tuning API only supports .jsonl files.
 *
 * Please contact us if you need to increase these storage limits.
 */
function filesUpload(client, request, options) {
    return new async_js_1.APIPromise($do(client, request, options));
}
async function $do(client, request, options) {
    const parsed = (0, schemas_js_1.safeParse)(request, (value) => operations.FilesApiRoutesUploadFileMultiPartBodyParams$outboundSchema
        .parse(value), "Input validation failed");
    if (!parsed.ok) {
        return [parsed, { status: "invalid" }];
    }
    const payload = parsed.value;
    const body = new FormData();
    if ((0, blobs_js_1.isBlobLike)(payload.file)) {
        (0, encodings_js_1.appendForm)(body, "file", payload.file);
    }
    else if ((0, streams_js_1.isReadableStream)(payload.file.content)) {
        const buffer = await (0, files_js_1.readableStreamToArrayBuffer)(payload.file.content);
        const blob = new Blob([buffer], { type: "application/octet-stream" });
        (0, encodings_js_1.appendForm)(body, "file", blob);
    }
    else {
        (0, encodings_js_1.appendForm)(body, "file", new Blob([payload.file.content], { type: "application/octet-stream" }), payload.file.fileName);
    }
    if (payload.purpose !== undefined) {
        (0, encodings_js_1.appendForm)(body, "purpose", payload.purpose);
    }
    const path = (0, url_js_1.pathToFunc)("/v1/files")();
    const headers = new Headers((0, primitives_js_1.compactMap)({
        Accept: "application/json",
    }));
    const secConfig = await (0, security_js_1.extractSecurity)(client._options.apiKey);
    const securityInput = secConfig == null ? {} : { apiKey: secConfig };
    const requestSecurity = (0, security_js_1.resolveGlobalSecurity)(securityInput);
    const context = {
        baseURL: options?.serverURL ?? client._baseURL ?? "",
        operationID: "files_api_routes_upload_file",
        oAuth2Scopes: [],
        resolvedSecurity: requestSecurity,
        securitySource: client._options.apiKey,
        retryConfig: options?.retries
            || client._options.retryConfig
            || { strategy: "none" },
        retryCodes: options?.retryCodes || ["429", "500", "502", "503", "504"],
    };
    const requestRes = client._createRequest(context, {
        security: requestSecurity,
        method: "POST",
        baseURL: options?.serverURL,
        path: path,
        headers: headers,
        body: body,
        timeoutMs: options?.timeoutMs || client._options.timeoutMs || -1,
    }, options);
    if (!requestRes.ok) {
        return [requestRes, { status: "invalid" }];
    }
    const req = requestRes.value;
    const doResult = await client._do(req, {
        context,
        errorCodes: ["4XX", "5XX"],
        retryConfig: context.retryConfig,
        retryCodes: context.retryCodes,
    });
    if (!doResult.ok) {
        return [doResult, { status: "request-error", request: req }];
    }
    const response = doResult.value;
    const [result] = await M.match(M.json(200, components.UploadFileOut$inboundSchema), M.fail("4XX"), M.fail("5XX"))(response);
    if (!result.ok) {
        return [result, { status: "complete", request: req, response }];
    }
    return [result, { status: "complete", request: req, response }];
}
//# sourceMappingURL=filesUpload.js.map