/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
import { PromptManagerConfigSchema } from './prompts.js';
/**
 * Configuration schemas using Zod for runtime validation
 */
export const ProviderConfigSchema = z.object({
    type: z.enum(['openai', 'anthropic', 'google', 'mistral', 'openrouter', 'custom', 'local']),
    apiKey: z.string().optional(),
    baseUrl: z.string().optional(),
    timeout: z.number().optional(),
    retries: z.number().optional(),
    headers: z.record(z.string()).optional(),
    proxy: z.string().optional(),
    enabled: z.boolean().optional(),
});
export const ToolConfigSchema = z.object({
    enabled: z.boolean(),
    timeout: z.number().optional(),
    retries: z.number().optional(),
    options: z.record(z.unknown()).optional(),
});
export const UIConfigSchema = z.object({
    theme: z.enum(['light', 'dark', 'auto']),
    animations: z.boolean(),
    verbose: z.boolean(),
    showTimestamps: z.boolean().optional(),
    showTokenCount: z.boolean().optional(),
    autoScroll: z.boolean().optional(),
    maxHistoryLines: z.number().optional(),
    colors: z.object({
        primary: z.string(),
        secondary: z.string(),
        success: z.string(),
        warning: z.string(),
        error: z.string(),
        info: z.string(),
    }).optional(),
});
export const LoggingConfigSchema = z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']),
    format: z.enum(['text', 'json']),
    output: z.enum(['console', 'file', 'both']),
    file: z.object({
        path: z.string(),
        maxSize: z.string(),
        maxFiles: z.number(),
        compress: z.boolean(),
    }).optional(),
    structured: z.boolean(),
    includeTimestamp: z.boolean(),
    includeLevel: z.boolean(),
    includeSource: z.boolean(),
});
export const ConfigurationSchema = z.object({
    version: z.string(),
    environment: z.enum(['development', 'production', 'test']),
    profile: z.string().optional(),
    defaultProvider: z.string(),
    providers: z.record(ProviderConfigSchema),
    tools: z.object({
        enabled: z.array(z.string()),
        file: ToolConfigSchema.optional(),
        git: ToolConfigSchema.optional(),
        web: ToolConfigSchema.optional(),
        shell: ToolConfigSchema.optional(),
        memory: ToolConfigSchema.optional(),
    }),
    prompts: PromptManagerConfigSchema,
    ui: UIConfigSchema,
    logging: LoggingConfigSchema,
    telemetry: z.object({
        enabled: z.boolean(),
        endpoint: z.string().optional(),
        apiKey: z.string().optional(),
        collectUsage: z.boolean(),
        collectErrors: z.boolean(),
        collectPerformance: z.boolean(),
        anonymize: z.boolean(),
    }),
    security: z.object({
        sandbox: z.object({
            enabled: z.boolean(),
            type: z.enum(['docker', 'podman', 'native']),
            image: z.string().optional(),
            allowNetworking: z.boolean(),
            allowFileSystem: z.boolean(),
            allowedPaths: z.array(z.string()).optional(),
            blockedPaths: z.array(z.string()).optional(),
        }),
        encryption: z.object({
            enabled: z.boolean(),
            algorithm: z.string(),
            keyDerivation: z.string(),
        }),
        authentication: z.object({
            required: z.boolean(),
            methods: z.array(z.string()),
            tokenExpiry: z.number(),
        }),
    }),
    performance: z.object({
        maxConcurrentRequests: z.number(),
        requestTimeout: z.number(),
        retryAttempts: z.number(),
        retryDelay: z.number(),
        caching: z.object({
            enabled: z.boolean(),
            ttl: z.number(),
            maxSize: z.number(),
        }),
        rateLimit: z.object({
            enabled: z.boolean(),
            requestsPerMinute: z.number(),
            burstLimit: z.number(),
        }),
    }),
    custom: z.record(z.unknown()).optional(),
});
//# sourceMappingURL=config.js.map