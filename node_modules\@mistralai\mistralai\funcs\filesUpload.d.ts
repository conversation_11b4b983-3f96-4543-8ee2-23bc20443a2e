import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Upload File
 *
 * @remarks
 * Upload a file that can be used across various endpoints.
 *
 * The size of individual files can be a maximum of 512 MB. The Fine-tuning API only supports .jsonl files.
 *
 * Please contact us if you need to increase these storage limits.
 */
export declare function filesUpload(client: MistralCore, request: operations.FilesApiRoutesUploadFileMultiPartBodyParams, options?: RequestOptions): APIPromise<Result<components.UploadFileOut, SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=filesUpload.d.ts.map