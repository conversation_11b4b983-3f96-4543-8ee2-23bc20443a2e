/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { PromptTemplate, PromptLoader, PromptLoaderConfig, PromptSource, Logger } from '@inkbytefo/s647-shared';
/**
 * Prompt loader registry
 * Manages multiple prompt loaders and coordinates loading from different sources
 */
export declare class PromptLoaderRegistry {
    private loaders;
    private logger;
    constructor(logger?: Logger);
    /**
     * Initialize the registry with loader configurations
     */
    initialize(configs: PromptLoaderConfig[]): Promise<void>;
    /**
     * Load prompts from all registered loaders
     */
    loadAllPrompts(): Promise<PromptTemplate[]>;
    /**
     * Get a specific loader
     */
    getLoader(source: PromptSource): PromptLoader | undefined;
    /**
     * Check if a prompt exists in any loader
     */
    exists(id: string): Promise<boolean>;
    /**
     * Save prompts to appropriate loaders
     */
    savePrompts(prompts: PromptTemplate[], source?: PromptSource): Promise<void>;
    /**
     * Create a loader instance based on configuration
     */
    private createLoader;
    /**
     * Get default global prompts path
     */
    private getDefaultGlobalPath;
    /**
     * Get default project prompts path
     */
    private getDefaultProjectPath;
    /**
     * Get default session prompts path
     */
    private getDefaultSessionPath;
}
//# sourceMappingURL=registry.d.ts.map