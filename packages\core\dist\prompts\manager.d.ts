/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { PromptManager, PromptManagerConfig, PromptTemplate, PromptContext, PromptSelector, RenderedPrompt, PromptValidationResult, PromptCategory, Logger } from '@inkbytefo/s647-shared';
/**
 * Prompt manager implementation
 * Handles loading, rendering, and management of system prompts
 */
export declare class PromptManagerImpl implements PromptManager {
    private loaderRegistry;
    private renderer;
    private builtInLibrary;
    private context;
    private cache;
    private config?;
    private logger;
    constructor(logger?: Logger);
    /**
     * Initialize the prompt manager
     */
    initialize(config: PromptManagerConfig): Promise<void>;
    /**
     * Get a prompt by selector
     */
    getPrompt(selector: PromptSelector): Promise<PromptTemplate | undefined>;
    /**
     * Render a prompt with context
     */
    renderPrompt(selector: PromptSelector, context: PromptContext): Promise<RenderedPrompt | undefined>;
    /**
     * Add a new prompt
     */
    addPrompt(template: PromptTemplate): Promise<void>;
    /**
     * Remove a prompt
     */
    removePrompt(id: string): Promise<void>;
    /**
     * Update a prompt
     */
    updatePrompt(id: string, template: Partial<PromptTemplate>): Promise<void>;
    /**
     * List prompts by category
     */
    listPrompts(category?: PromptCategory): Promise<PromptTemplate[]>;
    /**
     * Search prompts by query
     */
    searchPrompts(query: string): Promise<PromptTemplate[]>;
    /**
     * Validate a prompt template
     */
    validatePrompt(template: PromptTemplate): PromptValidationResult;
    /**
     * Get current context
     */
    getContext(): PromptContext;
    /**
     * Update context
     */
    updateContext(context: Partial<PromptContext>): void;
    /**
     * Load all prompts from all sources
     */
    private loadAllPrompts;
    /**
     * Get all prompts from all sources
     */
    private getAllPrompts;
    /**
     * Find the best matching prompt for a selector
     */
    private findBestMatch;
    /**
     * Check if a prompt matches the given context
     */
    private matchesContext;
}
//# sourceMappingURL=manager.d.ts.map