{"version": 3, "file": "built-in.js", "sourceRoot": "", "sources": ["../../../src/prompts/templates/built-in.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AASH;;GAEG;AACH,MAAM,OAAO,oBAAoB;IACvB,OAAO,GAAgC,IAAI,GAAG,EAAE,CAAC;IAEzD;QACE,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,EAAU;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAwB;QAC3C,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,GAAW;QACzB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAa;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC9D,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAC1E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;YAClC,IAAI,EAAE,6BAA6B;YACnC,WAAW,EAAE,4DAA4D;YACzE,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,QAAQ,CAAC;YAC1C,OAAO,EAAE;;;;;;;;;;;;;;;;;;;+EAmBgE;SAC1E,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;YAC9B,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,sDAAsD;YACnE,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;YACvC,OAAO,EAAE;;;;;;;;;;;;;;;;;;yEAkB0D;SACpE,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YACzB,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,mDAAmD;YAChE,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;YACzD,OAAO,EAAE;;;;;;;;;;;;;;;;;2CAiB4B;SACtC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC1B,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,2CAA2C;YACxD,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC1C,OAAO,EAAE;;;;;;;;;;;;;;;gGAeiF;SAC3F,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YACvB,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,oDAAoD;YACjE,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;YAC3C,OAAO,EAAE;;;;;;;;;;;;;;iHAckG;SAC5G,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;YAC9B,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,4DAA4D;YACzE,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,WAAW,CAAC;YAC/C,OAAO,EAAE;;;;;;;;;;;;;;;gFAeiE;SAC3E,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxB,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,4DAA4D;YACzE,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC;YAC1C,OAAO,EAAE;;;;;;;;;;;;;8EAa+D;SACzE,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxB,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,kCAAkC;YAC/C,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;YACpC,OAAO,EAAE;;;;;;;;;qEASsD;SAChE,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YACzB,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,sCAAsC;YACnD,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;YAC/C,OAAO,EAAE;;;;;;;;;2EAS4D;SACtE,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;YAC7B,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,4CAA4C;YACzD,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,CAAC;YACzC,OAAO,EAAE;;;;;;;;;sFASuE;SACjF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,EAAmB,EAAE,QAMtC;QACC,MAAM,MAAM,GAAmB;YAC7B,EAAE;YACF,QAAQ,EAAE;gBACR,GAAG,QAAQ;gBACX,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB;YACD,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC;YAClD,QAAQ,EAAE,EAAE,EAAE,wCAAwC;SACvD,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe;QACtC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,MAAM,KAAK,GAAG,uBAAuB,CAAC;QACtC,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;YAClC,IAAI,QAAQ,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5E,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;CACF"}