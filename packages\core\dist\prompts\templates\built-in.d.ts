/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { PromptTemplate, PromptLibrary, PromptCategory } from '@inkbytefo/s647-shared';
/**
 * Built-in prompt library with professional system prompts
 */
export declare class BuiltInPromptLibrary implements PromptLibrary {
    private prompts;
    constructor();
    /**
     * Get a prompt by ID
     */
    getPrompt(id: string): PromptTemplate | undefined;
    /**
     * Get all prompts
     */
    getAllPrompts(): PromptTemplate[];
    /**
     * Get prompts by category
     */
    getPromptsByCategory(category: PromptCategory): PromptTemplate[];
    /**
     * Get prompts by tag
     */
    getPromptsByTag(tag: string): PromptTemplate[];
    /**
     * Search prompts
     */
    searchPrompts(query: string): PromptTemplate[];
    /**
     * Initialize built-in prompts
     */
    private initializePrompts;
    /**
     * Add a prompt to the library
     */
    private addPrompt;
    /**
     * Extract variables from content (simple implementation)
     */
    private extractVariables;
}
//# sourceMappingURL=built-in.d.ts.map