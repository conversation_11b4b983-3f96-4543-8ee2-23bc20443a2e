/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  PromptManager,
  PromptManagerConfig,
  PromptTemplate,
  PromptContext,
  PromptSelector,
  RenderedPrompt,
  PromptValidationResult,
  PromptCategory,
  Async<PERSON><PERSON>ult,
  Logger,
} from '@inkbytefo/s647-shared';

import { PromptTemplateSchema } from '@inkbytefo/s647-shared';
import { PromptLoaderRegistry } from './loaders/registry.js';
import { PromptRenderer } from './renderers/mustache.js';
import { BuiltInPromptLibrary } from './templates/built-in.js';

/**
 * Prompt manager implementation
 * Handles loading, rendering, and management of system prompts
 */
export class PromptManagerImpl implements PromptManager {
  private loaderRegistry: PromptLoaderRegistry;
  private renderer: PromptRenderer;
  private builtInLibrary: BuiltInPromptLibrary;
  private context: PromptContext = {};
  private cache = new Map<string, PromptTemplate>();
  private config?: PromptManagerConfig;
  private logger: Logger | undefined;

  constructor(logger?: Logger) {
    this.logger = logger;
    this.loaderRegistry = new PromptLoaderRegistry(logger);
    this.renderer = new PromptRenderer();
    this.builtInLibrary = new BuiltInPromptLibrary();
  }

  /**
   * Initialize the prompt manager
   */
  async initialize(config: PromptManagerConfig): Promise<void> {
    this.config = config;
    this.logger?.info('Initializing Prompt Manager', { config });

    // Initialize loader registry
    await this.loaderRegistry.initialize(config.loaders);

    // Load all prompts
    await this.loadAllPrompts();

    this.logger?.info('Prompt Manager initialized successfully');
  }

  /**
   * Get a prompt by selector
   */
  async getPrompt(selector: PromptSelector): Promise<PromptTemplate | undefined> {
    this.logger?.debug('Getting prompt', { selector });

    // Try to find by ID first
    if (selector.id) {
      const cached = this.cache.get(selector.id);
      if (cached) {
        return cached;
      }

      // Check built-in library
      const builtIn = this.builtInLibrary.getPrompt(selector.id);
      if (builtIn) {
        return builtIn;
      }
    }

    // Search by criteria
    const allPrompts = await this.getAllPrompts();
    return this.findBestMatch(allPrompts, selector);
  }

  /**
   * Render a prompt with context
   */
  async renderPrompt(
    selector: PromptSelector,
    context: PromptContext
  ): Promise<RenderedPrompt | undefined> {
    const template = await this.getPrompt(selector);
    if (!template) {
      this.logger?.warn('Prompt not found', { selector });
      return undefined;
    }

    // Merge context
    const mergedContext = { ...this.context, ...context };

    try {
      const rendered = await this.renderer.render(template, mergedContext);
      this.logger?.debug('Prompt rendered successfully', { 
        templateId: template.id,
        contextKeys: Object.keys(mergedContext)
      });
      return rendered;
    } catch (error) {
      this.logger?.error('Failed to render prompt', { 
        templateId: template.id,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Add a new prompt
   */
  async addPrompt(template: PromptTemplate): Promise<void> {
    const validation = this.validatePrompt(template);
    if (!validation.valid) {
      throw new Error(`Invalid prompt template: ${validation.errors.join(', ')}`);
    }

    this.cache.set(template.id, template);
    this.logger?.info('Prompt added', { id: template.id });
  }

  /**
   * Remove a prompt
   */
  async removePrompt(id: string): Promise<void> {
    this.cache.delete(id);
    this.logger?.info('Prompt removed', { id });
  }

  /**
   * Update a prompt
   */
  async updatePrompt(id: string, template: Partial<PromptTemplate>): Promise<void> {
    const existing = this.cache.get(id);
    if (!existing) {
      throw new Error(`Prompt not found: ${id}`);
    }

    const updated = { ...existing, ...template, id };
    const validation = this.validatePrompt(updated);
    if (!validation.valid) {
      throw new Error(`Invalid prompt template: ${validation.errors.join(', ')}`);
    }

    this.cache.set(id, updated);
    this.logger?.info('Prompt updated', { id });
  }

  /**
   * List prompts by category
   */
  async listPrompts(category?: PromptCategory): Promise<PromptTemplate[]> {
    const allPrompts = await this.getAllPrompts();
    
    if (!category) {
      return allPrompts;
    }

    return allPrompts.filter(prompt => prompt.metadata.category === category);
  }

  /**
   * Search prompts by query
   */
  async searchPrompts(query: string): Promise<PromptTemplate[]> {
    const allPrompts = await this.getAllPrompts();
    const lowerQuery = query.toLowerCase();

    return allPrompts.filter(prompt => 
      prompt.metadata.name.toLowerCase().includes(lowerQuery) ||
      prompt.metadata.description.toLowerCase().includes(lowerQuery) ||
      prompt.metadata.tags?.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
      prompt.content.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Validate a prompt template
   */
  validatePrompt(template: PromptTemplate): PromptValidationResult {
    try {
      PromptTemplateSchema.parse(template);
      
      // Additional validation
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check for required variables
      const variables = this.renderer.extractVariables(template.content);
      if (template.variables) {
        const missingVars = template.variables.filter(v => !variables.includes(v));
        if (missingVars.length > 0) {
          warnings.push(`Declared variables not used: ${missingVars.join(', ')}`);
        }
      }

      // Check for undefined variables
      const undeclaredVars = variables.filter((v: string) => !template.variables?.includes(v));
      if (undeclaredVars.length > 0) {
        warnings.push(`Undeclared variables used: ${undeclaredVars.join(', ')}`);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
      };
    } catch (error) {
      return {
        valid: false,
        errors: [error instanceof Error ? error.message : String(error)],
        warnings: [],
      };
    }
  }

  /**
   * Get current context
   */
  getContext(): PromptContext {
    return { ...this.context };
  }

  /**
   * Update context
   */
  updateContext(context: Partial<PromptContext>): void {
    // Filter out undefined values to satisfy exactOptionalPropertyTypes
    const filteredContext: Partial<PromptContext> = {};
    for (const [key, value] of Object.entries(context)) {
      if (value !== undefined) {
        (filteredContext as any)[key] = value;
      }
    }
    this.context = { ...this.context, ...filteredContext };
    this.logger?.debug('Context updated', { context: Object.keys(context) });
  }

  /**
   * Load all prompts from all sources
   */
  private async loadAllPrompts(): Promise<void> {
    try {
      const prompts = await this.loaderRegistry.loadAllPrompts();
      
      // Clear cache and reload
      this.cache.clear();
      
      for (const prompt of prompts) {
        this.cache.set(prompt.id, prompt);
      }

      this.logger?.info('Loaded prompts', { count: prompts.length });
    } catch (error) {
      this.logger?.error('Failed to load prompts', { 
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get all prompts from all sources
   */
  private async getAllPrompts(): Promise<PromptTemplate[]> {
    const cached = Array.from(this.cache.values());
    const builtIn = this.builtInLibrary.getAllPrompts();
    
    // Merge and deduplicate (cache takes priority)
    const allPrompts = [...cached];
    const cachedIds = new Set(cached.map(p => p.id));
    
    for (const prompt of builtIn) {
      if (!cachedIds.has(prompt.id)) {
        allPrompts.push(prompt);
      }
    }

    return allPrompts;
  }

  /**
   * Find the best matching prompt for a selector
   */
  private findBestMatch(prompts: PromptTemplate[], selector: PromptSelector): PromptTemplate | undefined {
    let candidates = prompts;

    // Filter by category
    if (selector.category) {
      candidates = candidates.filter(p => p.metadata.category === selector.category);
    }

    // Filter by tags
    if (selector.tags && selector.tags.length > 0) {
      candidates = candidates.filter(p => 
        selector.tags!.some(tag => p.metadata.tags?.includes(tag))
      );
    }

    // Filter by context (basic matching)
    if (selector.context) {
      candidates = candidates.filter(p => this.matchesContext(p, selector.context!));
    }

    // Sort by priority and return the best match
    candidates.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    
    return candidates[0];
  }

  /**
   * Check if a prompt matches the given context
   */
  private matchesContext(prompt: PromptTemplate, context: Partial<PromptContext>): boolean {
    if (!prompt.conditions) {
      return true;
    }

    return prompt.conditions.every(condition => {
      const value = context[condition.variable as keyof PromptContext];
      
      switch (condition.operator) {
        case 'equals':
          return value === condition.value;
        case 'not-equals':
          return value !== condition.value;
        case 'contains':
          return typeof value === 'string' && typeof condition.value === 'string' 
            && value.includes(condition.value);
        case 'not-contains':
          return typeof value === 'string' && typeof condition.value === 'string' 
            && !value.includes(condition.value);
        case 'exists':
          return value !== undefined && value !== null;
        case 'not-exists':
          return value === undefined || value === null;
        default:
          return true;
      }
    });
  }
}
